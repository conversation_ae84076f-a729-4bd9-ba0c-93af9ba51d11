FROM docker-registry.telemidia.net.br/alma-base:latest

# ---------------------------------------------------------------
# Cria serviço para o print-server
COPY print-server.service /etc/systemd/system/print-server.service

# ---------------------------------------------------------------
# Copia configurações e drivers para o CUPS
RUN mkdir -p /usr/share/cups/model/elgin && mkdir -p /etc/cups
COPY cups /etc/cups
COPY elgin /usr/share/cups/model/elgin
COPY rastertohprt /usr/lib/cups/filter/rastertohprt

# ---------------------------------------------------------------
# Copia configurações do Apache
RUN mkdir -p /etc/httpd/conf.d
COPY httpd-conf/* /etc/httpd/conf.d/

# Aumenta o número de arquivos abertos por serviço (o CUPS não inicia se não definir um valor adequado)
RUN mkdir -p /etc/systemd/system/cups.service.d
RUN printf "[Service]\nLimitNOFILE=1024\n" > /etc/systemd/system/cups.service.d/override.conf

# ---------------------------------------------------------------
# Instala NVM
# Usa bash como shell padrão
SHELL ["/bin/bash", "-o", "pipefail", "-c"]
# Configura NVM
ENV NODE_VERSION=16.20.2
ENV NVM_DIR=/usr/local/nvm
ENV BASH_ENV=/root/.bash_env
RUN mkdir -p $NVM_DIR && touch "${BASH_ENV}" \
    && echo 'export NVM_DIR="$NVM_DIR"' >> "${BASH_ENV}" \
    && echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> "${BASH_ENV}" \
    && echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"' >> "${BASH_ENV}" \
    && echo '. "${BASH_ENV}"' >> /root/.bashrc
# Instala NVM
RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | PROFILE="${BASH_ENV}" bash
# Instala Node versão $NODE_VERSION e define como default
RUN bash -c "source ${BASH_ENV} && nvm install $NODE_VERSION && nvm alias default $NODE_VERSION"

ENV NODE_PATH=$NVM_DIR/v$NODE_VERSION/lib/node_modules
ENV PATH=$NVM_DIR/versions/node/v$NODE_VERSION/bin:$PATH

# ---------------------------------------------------------------
# Instala pacotes necessários para o print-server
RUN dnf config-manager --set-enabled powertools && \
    dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-8.noarch.rpm && \
    dnf install -y https://rpms.remirepo.net/enterprise/remi-release-8.rpm && \
    dnf install -y cups cups-client cups-filesystem \
    cups-filters cups-filters-libs cups-ipptool cups-libs \
    httpd curl git php74 php74-php-cli \
    php74-php-common php74-php-fpm php74-php-gd php74-php-json \
    php74-php-mbstring php74-php-mysqlnd php74-php-opcache \
    php74-php-pdo php74-php-pgsql php74-php-xml

# Usa secret no momento do clone
RUN --mount=type=secret,id=gitcreds \
    GIT_CRED=$(cat /run/secrets/gitcreds) && \
    git clone http://$<EMAIL>/Telemidia/print-server.git /srv/print-server && \
    git clone http://$<EMAIL>/Telemidia/segunda-via.git /var/www/segunda-via && \
    git clone http://$<EMAIL>/Telemidia/segunda-via.git /var/www/segunda-via-dev

RUN mkdir -p /etc/systemd/system/httpd.service.d &&\
    echo -e '[Service]\nExecStartPre=mkdir -p /var/log/httpd "Executando pré-start do HTTPD"' > /etc/systemd/system/httpd.service.d/execstartpre.conf

RUN cd /var/www/segunda-via-dev && git checkout dev && \
    cd /srv/print-server && npm install

RUN systemctl enable httpd && \
    systemctl enable cups && \
    systemctl enable print-server

# Corrige o PATH global do systemd
RUN echo "DefaultEnvironment=PATH=$NVM_DIR/versions/node/v$NODE_VERSION/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" \
    >> /etc/systemd/system.conf

RUN mkdir -p /etc/systemd/system.conf.d && \
    echo -e "[Manager]\nDefaultEnvironment=PATH=$NVM_DIR/versions/node/v$NODE_VERSION/bin:$PATH" \
    > /etc/systemd/system.conf.d/node.conf