=== Iniciando análise completa do servidor ===
Data/Hora: seg set 15 22:36:37 -03 2025

=== Detectando Sistema Operacional ===
Sistema: centos 8
Base image selecionada: rockylinux:8

=== Coletando lista de pacotes instalados ===
Sistema baseado em RPM detectado
Total de pacotes instalados: 362
=== Detectando pacotes personalizados ===
=== Repositórios habilitados ===
appstream              CentOS Linux 8 - AppStream
baseos                 CentOS Linux 8 - BaseOS
epel                   Extra Packages for Enterprise Linux 8 - x86_64
epel-modular           Extra Packages for Enterprise Linux Modular 8 - x86_64
extras                 CentOS Linux 8 - Extras
zabbix                 Zabbix Official Repository - x86_64
zabbix-non-supported   Zabbix Official Repository non-supported - x86_64
Pacotes personalizados detectados: 74
Principais pacotes personalizados:
  - bind-export-libs
  - certbot
  - emacs-filesystem
  - epel-release
  - git
  - git-core
  - git-core-doc
  - httpd
  - httpd-filesystem
  - httpd-tools
  - iptables-libs
  - nano
  - nginx-filesystem
  - php
  - php-cli
  - php-common
  - php-fpm
  - php-gd
  - php-json
  - php-mbstring

=== Coletando serviços habilitados ===
Serviços habilitados:
  - autovt@.service
  - bacula-fd.service
  - crond.service
  - getty@.service
  - httpd.service
  - import-state.service
  - kdump.service
  - loadmodules.service
  - nis-domainname.service
  - nslcd.service

=== Coletando crontabs ===
Crontabs coletados em: detected_crontabs.txt

=== Detectando arquivos de configuração importantes ===
Arquivos de configuração importantes encontrados:
  - /etc/httpd/conf/httpd.conf
  - /etc/php.ini
  - /etc/ssh/sshd_config
  - /etc/sudoers
  - /etc/hosts
  - /etc/resolv.conf
  - /etc/exports
  - /etc/logrotate.d/bacula
  - /etc/logrotate.d/btmp
  - /etc/logrotate.d/dnf

=== Coletando usuários do sistema ===
Nenhum usuário personalizado encontrado

=== Detectando portas em uso ===
Portas detectadas: 22 80 443 9102 10050 33750 34763 35235 35845 37458 38767 41454 42001 42043 43999 44604 

=== Gerando Dockerfile avançado ===
================================
ANÁLISE COMPLETA FINALIZADA
================================
Arquivos gerados:
  - output.Dockerfile (Dockerfile principal)
  - docker-entrypoint.sh (Script de inicialização)
  - backup_server.sh (Script de backup)
  - docker_info.log (Log da análise)
  - detected_packages.txt (Lista completa de pacotes)
  - custom_packages.txt (Pacotes personalizados)
  - detected_services.txt (Serviços habilitados)
  - detected_crontabs.txt (Crontabs coletados)
  - detected_configs.txt (Arquivos de configuração)
  - detected_users.txt (Usuários personalizados)

PRÓXIMOS PASSOS:
1. Execute './backup_server.sh' para fazer backup dos dados
2. Copie os arquivos de crontab: cp detected_crontabs.txt crontabs.txt
3. Construa a imagem: docker build -t servidor-replica .
4. Execute o container com volumes persistentes

Script finalizado com sucesso!
