# Guia de Início <PERSON>ápido - Container Migration Tool

## 🚀 Começando em 5 Minutos

### 1. Pré-requisitos Rápid<PERSON>
```bash
# Instalar dependências essenciais
yum install -y docker rsync openssh-clients jq curl git

# Iniciar Docker
systemctl enable docker && systemctl start docker

# Configurar SSH para servidor de homologação
ssh-copy-id root@************
```

### 2. Executar a Ferramenta
```bash
# Como root
sudo ./migrate.sh
```

### 3. Fluxo Básico
1. **Menu Principal** → Escolha "1. Iniciar nova migração"
2. **Nome do Container** → Digite um nome único (ex: "meu-servidor")
3. **Varredura** → Aguarde análise automática do sistema
4. **Seleção** → Escolha componentes por categoria:
   - `a` = todos, `n` = nenhum, `s` = específicos, `q` = pular
5. **Dockerfile** → Revise o arquivo gerado
6. **Build** → Confirme para construir a imagem
7. **Deploy** → Confirme para enviar para homologação

## 📋 Exemplo de Uso Típico

### Cenário: Servidor Web com PHP e MySQL
```
🔄 Varredura detectou:
   - Pacotes: httpd, php74, mysql-server
   - Configurações: /etc/httpd/conf/httpd.conf, /etc/php.ini
   - Diretórios: /var/www, /var/lib/mysql

📝 Seleções recomendadas:
   - WEBSERVER: Selecionar todos (httpd, php74)
   - DATABASE: Selecionar mysql-server
   - WEB: Selecionar /var/www
   - DATABASE: Selecionar /var/lib/mysql
   - WEBSERVER: Selecionar configurações do Apache/PHP

✅ Resultado: Container funcional com mesmo ambiente
```

## 🎯 Comandos Essenciais

### Durante o Processo
```bash
# Verificar status
./test-tool.sh

# Ver logs em tempo real
tail -f /var/log/container-migration.log

# Cancelar processo
Ctrl+C (dados são preservados)
```

### Após Deploy
```bash
# No servidor de homologação (************)
pnet-container status meu-servidor
docker logs meu-servidor
ssh root@<IP_DO_CONTAINER>
```

## 🔧 Solução de Problemas Rápidos

### Erro: "Docker não encontrado"
```bash
yum install -y docker
systemctl start docker
```

### Erro: "SSH não conecta"
```bash
ssh-copy-id root@************
# ou
ssh-keygen -R ************ && ssh-copy-id root@************
```

### Erro: "Espaço insuficiente"
```bash
# Limpar espaço
docker system prune -f
yum clean all
```

### Erro: "jq não encontrado"
```bash
yum install -y jq
```

## 📁 Arquivos Importantes

### Durante o Processo
- `work/<container>/scan_results.json` - Resultados da varredura
- `work/<container>/Dockerfile` - Dockerfile gerado
- `work/<container>/backup/` - Backup dos dados

### Após Conclusão
- `/etc/sysconfig/pnet/<container>` - Configuração PNET (no servidor)
- `/var/docker/<container>/` - Dados do container (no servidor)

## 🎨 Dicas de Uso

### Seleção Inteligente
```
# Exemplos de seleção específica:
1,3,5     → Itens 1, 3 e 5
1-5       → Itens de 1 a 5
1,3,7-10  → Itens 1, 3 e de 7 a 10
```

### Categorias Comuns
- **WEBSERVER**: Apache, Nginx, configurações web
- **DATABASE**: MySQL, PostgreSQL, Redis, dados
- **RUNTIME**: PHP, Python, Node.js, Java
- **APPLICATION**: Diretórios /opt, /srv
- **WEB**: Diretórios /var/www

### Boas Práticas
1. **Sempre teste primeiro** com nome temporário
2. **Documente seleções** para futuras migrações
3. **Verifique logs** se algo der errado
4. **Faça backup** dos dados originais

## 🔄 Fluxo de Continuação

### Retomar Migração Interrompida
```
Menu Principal → 2. Continuar migração existente
→ Escolher container → Continuar do ponto onde parou
```

### Atualizar Container Existente
```
Menu Principal → 2. Continuar migração existente
→ Escolher container → 3. Refazer seleções
```

## 📞 Suporte Rápido

### Coletar Informações para Suporte
```bash
# Informações do sistema
./test-tool.sh > suporte-info.txt

# Logs recentes
tail -100 /var/log/container-migration.log >> suporte-info.txt

# Configuração atual
cat config/migration.conf >> suporte-info.txt
```

### Problemas Mais Comuns
1. **Dependências faltando** → Execute `./test-tool.sh`
2. **SSH não configurado** → Execute `ssh-copy-id root@************`
3. **Pouco espaço** → Execute `docker system prune -f`
4. **Permissões** → Execute como root: `sudo ./migrate.sh`

## 🎯 Casos de Uso Rápidos

### Migração Simples (Servidor Web)
```
1. Execute: sudo ./migrate.sh
2. Nome: "servidor-web"
3. Seleções: WEBSERVER=todos, WEB=/var/www
4. Build: Sim
5. Deploy: Sim
6. Teste: http://<IP_GERADO>
```

### Migração Completa (LAMP Stack)
```
1. Execute: sudo ./migrate.sh
2. Nome: "lamp-server"
3. Seleções: 
   - WEBSERVER=todos
   - DATABASE=todos
   - RUNTIME=PHP
   - WEB=/var/www
   - DATABASE=/var/lib/mysql
4. Build: Sim
5. Deploy: Sim
6. Teste: Aplicação completa
```

### Migração Seletiva (Apenas Aplicação)
```
1. Execute: sudo ./migrate.sh
2. Nome: "minha-app"
3. Seleções:
   - APPLICATION=/opt/minha-app
   - RUNTIME=específicos necessários
4. Build: Sim
5. Deploy: Sim
6. Configurar manualmente serviços externos
```

---

**💡 Dica**: Para primeira vez, use um servidor de teste e nome temporário para se familiarizar com o processo!

**📖 Documentação Completa**: Consulte [README.md](README.md) para informações detalhadas.

**🛠️ Instalação Detalhada**: Consulte [INSTALL.md](INSTALL.md) para configuração avançada.
