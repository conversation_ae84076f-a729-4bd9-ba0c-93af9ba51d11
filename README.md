# Container Migration Tool

Ferramenta completa para automatizar a migração de servidores CentOS para containers Docker, seguindo o padrão da infraestrutura PNET.

## 🚀 Características

- **Varredura Inteligente**: Analisa automaticamente pacotes, configurações e diretórios do servidor
- **Seleção Interativa**: Interface amigável para escolher componentes a migrar
- **Geração Automática**: Cria Dockerfile otimizado baseado no padrão existente
- **Build Integrado**: Constrói e testa a imagem Docker automaticamente
- **Deploy Automatizado**: Faz deploy direto no servidor de homologação (************)
- **Configuração PNET**: Gera automaticamente configurações compatíveis com pnet-container

## 📋 Pré-requisitos

### No servidor origem (onde será executado):
- CentOS 7/8 ou AlmaLinux/Rocky Linux
- Acesso root
- Docker instalado
- Conectividade SSH para ************ (chave configurada)

### Pacotes necessários:
```bash
yum install -y docker rsync openssh-clients jq curl git
```

### No servidor de destino (************):
- Sistema pnet-container configurado
- Acesso SSH como root
- Diretório /var/docker disponível

## 🛠️ Instalação

1. Clone ou copie os arquivos para o servidor:
```bash
git clone <repositorio> /opt/container-migration-tool
cd /opt/container-migration-tool
```

2. Torne o script executável:
```bash
chmod +x migrate.sh
```

3. Execute como root:
```bash
sudo ./migrate.sh
```

## 📖 Como usar

### 1. Execução Básica
```bash
sudo ./migrate.sh
```

O programa apresentará um menu interativo com as seguintes opções:
- **Iniciar nova migração**: Processo completo do zero
- **Continuar migração existente**: Retomar trabalho anterior
- **Configurações**: Ajustar parâmetros (futuro)
- **Ajuda**: Documentação integrada

### 2. Fluxo de Trabalho

#### Passo 1: Nome do Container
- Digite o nome desejado para o container
- Deve seguir padrão: letras, números, _ e -

#### Passo 2: Varredura do Sistema
O sistema analisa automaticamente:
- **Pacotes instalados**: Identifica pacotes customizados vs. base system
- **Serviços habilitados**: Lista serviços que devem ser mantidos
- **Diretórios de aplicação**: Encontra dados em /opt, /srv, /var/www, etc.
- **Configurações importantes**: Localiza arquivos de config relevantes
- **Portas em uso**: Detecta serviços de rede

#### Passo 3: Seleção de Componentes
Interface interativa por categoria:
- **Pacotes**: Escolha por categoria (webserver, database, runtime, etc.)
- **Configurações**: Selecione arquivos de config por tipo
- **Diretórios**: Escolha dados de aplicação a preservar

Opções disponíveis:
- `a` - Selecionar todos da categoria
- `n` - Não selecionar nenhum
- `s` - Seleção específica (ex: 1,3,5-7)
- `q` - Pular categoria

#### Passo 4: Geração do Dockerfile
- Cria Dockerfile otimizado baseado no template padrão
- Gera scripts auxiliares (entrypoint, backup, docker-compose)
- Permite visualização antes de prosseguir

#### Passo 5: Build da Imagem
- Executa backup automático dos dados selecionados
- Constrói imagem Docker com tag datada
- Testa a imagem criada
- Opção de push para registry

#### Passo 6: Deploy para Homologação
- Sincroniza dados via rsync para ************
- Cria configuração PNET automaticamente
- Gera configurações de rede (IP automático)
- Executa pnet-container create/start
- Verifica status final

## 📁 Estrutura de Arquivos

```
container-migration-tool/
├── migrate.sh                 # Script principal
├── src/
│   ├── main.sh                # Interface principal
│   └── lib/
│       ├── colors.sh          # Funções de formatação
│       ├── utils.sh           # Utilitários gerais
│       ├── scanner.sh         # Varredura do sistema
│       ├── selector.sh        # Interface de seleção
│       ├── dockerfile_generator.sh # Geração do Dockerfile
│       ├── builder.sh         # Build da imagem
│       └── deployer.sh        # Deploy para homologação
├── templates/                 # Templates de configuração
├── config/                    # Arquivos de configuração
└── work/                      # Diretório de trabalho (criado automaticamente)
    └── <container_name>/
        ├── scan_results.json  # Resultados da varredura
        ├── selected_*.txt     # Componentes selecionados
        ├── Dockerfile         # Dockerfile gerado
        ├── backup/            # Backup dos dados
        └── *.log             # Logs do processo
```

## 🔧 Configuração Avançada

### Arquivo de Configuração
Edite `config/migration.conf` para ajustar:
- Servidor de destino
- Faixas de IP
- Timeouts
- Exclusões de backup
- Recursos mínimos

### Variáveis de Ambiente
```bash
export MIGRATION_DEBUG=1        # Ativar debug
export MIGRATION_STAGING_SERVER="************"
export MIGRATION_DOCKER_REGISTRY="docker-registry.telemidia.net.br"
```

## 📊 Monitoramento e Logs

### Logs do Sistema
- Log principal: `/var/log/container-migration.log`
- Logs de build: `work/<container>/build.log`
- Logs de deploy: `work/<container>/deploy.log`

### Comandos de Monitoramento
```bash
# Status do container
pnet-container status <container_name>

# Logs do container
docker logs <container_name>

# Acesso ao container
docker exec -it <container_name> bash
```

## 🚨 Troubleshooting

### Problemas Comuns

#### 1. Erro de SSH
```bash
# Verificar conectividade
ssh root@************ echo "OK"

# Configurar chave SSH se necessário
ssh-copy-id root@************
```

#### 2. Falha no Build
```bash
# Verificar logs
tail -f work/<container>/build.log

# Verificar espaço em disco
df -h

# Limpar cache do Docker
docker system prune -f
```

#### 3. Container não inicia
```bash
# Verificar configuração PNET
cat /etc/sysconfig/pnet/<container_name>

# Verificar logs do pnet-container
journalctl -u pnet-container -f

# Verificar status do Docker
systemctl status docker
```

#### 4. Problemas de Rede
```bash
# Verificar IP do container
ping <container_ip>

# Verificar configuração de rede
docker exec <container> ip addr show

# Verificar roteamento
docker exec <container> ip route show
```

## 🔄 Operações de Manutenção

### Backup Manual
```bash
# Executar backup dos dados
cd work/<container_name>
./backup.sh
```

### Atualização do Container
```bash
# Usar opção "Continuar migração existente"
# Selecionar container existente
# Escolher "Refazer seleções" ou "Fazer deploy"
```

### Rollback
```bash
# Parar container
pnet-container stop <container_name>

# Remover container
pnet-container remove <container_name>

# Remover configuração
rm /etc/sysconfig/pnet/<container_name>

# Remover dados (opcional)
rm -rf /var/docker/<container_name>
```

## 📈 Boas Práticas

1. **Sempre teste localmente primeiro**
2. **Faça backup dos dados originais**
3. **Documente configurações específicas**
4. **Monitore recursos após deploy**
5. **Mantenha logs para auditoria**

## 🤝 Contribuição

Para contribuir com melhorias:
1. Teste em ambiente controlado
2. Documente mudanças
3. Mantenha compatibilidade com padrão existente
4. Adicione logs adequados

## 📄 Licença

Ferramenta interna - Uso restrito à infraestrutura PNET.

---

**Versão**: 1.0  
**Última atualização**: $(date)  
**Suporte**: Equipe de Infraestrutura
