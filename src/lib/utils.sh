#!/bin/bash

# ============================================================================
# Utility functions
# ============================================================================

# Verificar dependências do sistema
check_dependencies() {
    local missing_deps=()
    local required_commands=(
        "rpm:rpm"
        "yum:yum"
        "systemctl:systemd"
        "docker:docker"
        "rsync:rsync"
        "ssh:openssh-clients"
        "jq:jq"
        "curl:curl"
        "git:git"
    )
    
    print_step "Verificando dependências..."
    
    for dep in "${required_commands[@]}"; do
        local cmd="${dep%:*}"
        local package="${dep#*:}"
        
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_deps+=("$package")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "Dependências não encontradas:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        echo
        echo "Instale as dependências com:"
        echo "  yum install -y ${missing_deps[*]}"
        return 1
    fi
    
    print_success "Todas as dependências estão instaladas"
    return 0
}

# Verificar se é root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "Este script deve ser executado como root"
        return 1
    fi
    return 0
}

# Verificar conectividade SSH
check_ssh_connectivity() {
    local host="$1"
    local user="${2:-root}"
    
    print_step "Verificando conectividade SSH para $user@$host..."
    
    if ssh -o ConnectTimeout=5 -o BatchMode=yes "$user@$host" exit 2>/dev/null; then
        print_success "Conectividade SSH OK"
        return 0
    else
        print_error "Não foi possível conectar via SSH para $user@$host"
        print_info "Verifique se a chave SSH está configurada corretamente"
        return 1
    fi
}

# Validar nome do container
validate_container_name() {
    local name="$1"
    
    if [[ -z "$name" ]]; then
        print_error "Nome do container não pode estar vazio"
        return 1
    fi
    
    if [[ ! "$name" =~ ^[a-zA-Z0-9][a-zA-Z0-9_-]*$ ]]; then
        print_error "Nome inválido! Use apenas letras, números, _ e -"
        return 1
    fi
    
    if [[ ${#name} -gt 50 ]]; then
        print_error "Nome muito longo! Máximo 50 caracteres"
        return 1
    fi
    
    return 0
}

# Criar backup de arquivo
backup_file() {
    local file="$1"
    local backup_dir="${2:-$(dirname "$file")}"
    
    if [[ ! -f "$file" ]]; then
        print_warning "Arquivo não existe: $file"
        return 1
    fi
    
    local backup_name="$(basename "$file").backup.$(date +%Y%m%d_%H%M%S)"
    local backup_path="$backup_dir/$backup_name"
    
    if cp "$file" "$backup_path"; then
        print_success "Backup criado: $backup_path"
        echo "$backup_path"
        return 0
    else
        print_error "Falha ao criar backup de $file"
        return 1
    fi
}

# Verificar espaço em disco
check_disk_space() {
    local path="$1"
    local required_mb="${2:-1000}"  # 1GB por padrão
    
    local available_mb
    available_mb=$(df -m "$path" | awk 'NR==2 {print $4}')
    
    if [[ "$available_mb" -lt "$required_mb" ]]; then
        print_error "Espaço insuficiente em $path"
        print_info "Disponível: ${available_mb}MB, Necessário: ${required_mb}MB"
        return 1
    fi
    
    print_success "Espaço em disco suficiente: ${available_mb}MB disponível"
    return 0
}

# Gerar MAC address aleatório
generate_random_mac() {
    printf "02:%02x:%02x:%02x:%02x:%02x\n" \
        $((RANDOM % 256)) \
        $((RANDOM % 256)) \
        $((RANDOM % 256)) \
        $((RANDOM % 256)) \
        $((RANDOM % 256))
}

# Gerar IP aleatório na faixa
generate_random_ip() {
    local base_ip="${1:-172.16.14}"
    local start_range="${2:-100}"
    local end_range="${3:-200}"
    
    local last_octet=$((RANDOM % (end_range - start_range + 1) + start_range))
    echo "${base_ip}.${last_octet}"
}

# Verificar se IP está em uso
check_ip_in_use() {
    local ip="$1"
    local timeout="${2:-3}"
    
    if ping -c 1 -W "$timeout" "$ip" >/dev/null 2>&1; then
        return 0  # IP em uso
    else
        return 1  # IP livre
    fi
}

# Encontrar IP livre na faixa
find_free_ip() {
    local base_ip="${1:-172.16.14}"
    local start_range="${2:-100}"
    local end_range="${3:-200}"
    local max_attempts=50
    
    for ((i=0; i<max_attempts; i++)); do
        local ip
        ip=$(generate_random_ip "$base_ip" "$start_range" "$end_range")
        
        if ! check_ip_in_use "$ip"; then
            echo "$ip"
            return 0
        fi
    done
    
    print_error "Não foi possível encontrar IP livre na faixa $base_ip.$start_range-$end_range"
    return 1
}

# Converter bytes para formato legível
bytes_to_human() {
    local bytes="$1"
    local units=("B" "KB" "MB" "GB" "TB")
    local unit=0
    
    while [[ "$bytes" -gt 1024 && "$unit" -lt $((${#units[@]} - 1)) ]]; do
        bytes=$((bytes / 1024))
        ((unit++))
    done
    
    echo "${bytes}${units[unit]}"
}

# Verificar se processo está rodando
is_process_running() {
    local process_name="$1"
    pgrep -f "$process_name" >/dev/null 2>&1
}

# Aguardar processo terminar
wait_for_process() {
    local process_name="$1"
    local timeout="${2:-30}"
    local interval="${3:-1}"
    local elapsed=0
    
    while is_process_running "$process_name" && [[ "$elapsed" -lt "$timeout" ]]; do
        sleep "$interval"
        elapsed=$((elapsed + interval))
    done
    
    if is_process_running "$process_name"; then
        print_warning "Processo $process_name ainda está rodando após ${timeout}s"
        return 1
    else
        print_success "Processo $process_name terminou"
        return 0
    fi
}

# Executar comando com retry
retry_command() {
    local max_attempts="$1"
    local delay="$2"
    shift 2
    local command=("$@")
    
    for ((i=1; i<=max_attempts; i++)); do
        if "${command[@]}"; then
            return 0
        else
            if [[ "$i" -lt "$max_attempts" ]]; then
                print_warning "Tentativa $i falhou, tentando novamente em ${delay}s..."
                sleep "$delay"
            fi
        fi
    done
    
    print_error "Comando falhou após $max_attempts tentativas: ${command[*]}"
    return 1
}

# Verificar se arquivo é binário
is_binary_file() {
    local file="$1"
    
    if [[ ! -f "$file" ]]; then
        return 1
    fi
    
    # Verificar primeiros bytes do arquivo
    if file "$file" | grep -q "text"; then
        return 1  # É texto
    else
        return 0  # É binário
    fi
}

# Calcular hash de arquivo
calculate_file_hash() {
    local file="$1"
    local algorithm="${2:-sha256}"
    
    if [[ ! -f "$file" ]]; then
        print_error "Arquivo não encontrado: $file"
        return 1
    fi
    
    case "$algorithm" in
        "md5")
            md5sum "$file" | cut -d' ' -f1
            ;;
        "sha1")
            sha1sum "$file" | cut -d' ' -f1
            ;;
        "sha256")
            sha256sum "$file" | cut -d' ' -f1
            ;;
        *)
            print_error "Algoritmo não suportado: $algorithm"
            return 1
            ;;
    esac
}

# Verificar integridade de arquivo
verify_file_integrity() {
    local file="$1"
    local expected_hash="$2"
    local algorithm="${3:-sha256}"
    
    local actual_hash
    actual_hash=$(calculate_file_hash "$file" "$algorithm")
    
    if [[ "$actual_hash" == "$expected_hash" ]]; then
        print_success "Integridade do arquivo verificada"
        return 0
    else
        print_error "Falha na verificação de integridade"
        print_info "Esperado: $expected_hash"
        print_info "Atual: $actual_hash"
        return 1
    fi
}

# Criar arquivo temporário seguro
create_temp_file() {
    local prefix="${1:-tmp}"
    local suffix="${2:-}"
    
    mktemp "/tmp/${prefix}.XXXXXX${suffix}"
}

# Criar diretório temporário seguro
create_temp_dir() {
    local prefix="${1:-tmp}"
    
    mktemp -d "/tmp/${prefix}.XXXXXX"
}

# Limpar arquivos temporários
cleanup_temp_files() {
    local temp_pattern="${1:-/tmp/tmp.*}"
    
    find /tmp -name "$(basename "$temp_pattern")" -type f -mtime +1 -delete 2>/dev/null || true
    find /tmp -name "$(basename "$temp_pattern")" -type d -mtime +1 -exec rm -rf {} + 2>/dev/null || true
}

# Verificar se string é um IP válido
is_valid_ip() {
    local ip="$1"
    local regex='^([0-9]{1,3}\.){3}[0-9]{1,3}$'
    
    if [[ ! "$ip" =~ $regex ]]; then
        return 1
    fi
    
    # Verificar se cada octeto está na faixa válida
    IFS='.' read -ra octets <<< "$ip"
    for octet in "${octets[@]}"; do
        if [[ "$octet" -gt 255 ]]; then
            return 1
        fi
    done
    
    return 0
}

# Verificar se porta está disponível
is_port_available() {
    local port="$1"
    local host="${2:-localhost}"
    
    if command -v nc >/dev/null 2>&1; then
        ! nc -z "$host" "$port" 2>/dev/null
    else
        ! ss -tuln | grep -q ":$port "
    fi
}

# Encontrar porta livre
find_free_port() {
    local start_port="${1:-8000}"
    local end_port="${2:-9000}"
    
    for ((port=start_port; port<=end_port; port++)); do
        if is_port_available "$port"; then
            echo "$port"
            return 0
        fi
    done
    
    print_error "Nenhuma porta livre encontrada na faixa $start_port-$end_port"
    return 1
}

# Log com timestamp
log_message() {
    local level="$1"
    local message="$2"
    local log_file="${3:-/var/log/container-migration.log}"
    
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] [$level] $message" >> "$log_file"
    
    # Também mostrar na tela se não for DEBUG
    if [[ "$level" != "DEBUG" ]]; then
        case "$level" in
            "ERROR")
                print_error "$message"
                ;;
            "WARNING")
                print_warning "$message"
                ;;
            "SUCCESS")
                print_success "$message"
                ;;
            *)
                print_info "$message"
                ;;
        esac
    fi
}

# Rotacionar logs
rotate_logs() {
    local log_file="$1"
    local max_size="${2:-10485760}"  # 10MB
    local max_files="${3:-5}"
    
    if [[ ! -f "$log_file" ]]; then
        return 0
    fi
    
    local file_size
    file_size=$(stat -c%s "$log_file" 2>/dev/null || echo 0)
    
    if [[ "$file_size" -gt "$max_size" ]]; then
        # Rotacionar arquivos existentes
        for ((i=max_files-1; i>=1; i--)); do
            if [[ -f "${log_file}.$i" ]]; then
                mv "${log_file}.$i" "${log_file}.$((i+1))"
            fi
        done
        
        # Mover arquivo atual
        mv "$log_file" "${log_file}.1"
        
        # Remover arquivos antigos
        for ((i=max_files+1; i<=10; i++)); do
            rm -f "${log_file}.$i"
        done
        
        print_info "Log rotacionado: $log_file"
    fi
}
