#!/bin/bash

# ============================================================================
# Container Builder - Constrói a imagem Docker
# ============================================================================

# Carregar dependências
source "$(dirname "${BASH_SOURCE[0]}")/colors.sh"
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# ============================================================================
# Configurações do builder
# ============================================================================

DOCKER_REGISTRY="docker-registry.telemidia.net.br"
BUILD_TIMEOUT=3600  # 1 hora

# ============================================================================
# Funções de build
# ============================================================================

# Função principal para build da imagem
build_image() {
    local container_name="$1"
    local work_dir="$2"
    
    print_header "BUILD DA IMAGEM DOCKER"
    
    # Verificar se Docker está disponível
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker não está instalado ou não está no PATH"
        return 1
    fi
    
    # Verificar se Docker daemon está rodando
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker daemon não está rodando"
        return 1
    fi
    
    # Verificar se Dockerfile existe
    local dockerfile="$work_dir/Dockerfile"
    if [[ ! -f "$dockerfile" ]]; then
        print_error "Dockerfile não encontrado: $dockerfile"
        return 1
    fi
    
    # Preparar contexto de build
    if ! prepare_build_context "$work_dir"; then
        print_error "Falha ao preparar contexto de build"
        return 1
    fi
    
    # Construir imagem
    local image_tag="$DOCKER_REGISTRY/$container_name"
    local date_tag="$(date +%Y-%m-%d)"
    
    print_step "Construindo imagem: $image_tag:$date_tag"
    
    # Executar build
    if build_docker_image "$work_dir" "$image_tag" "$date_tag"; then
        print_success "Imagem construída com sucesso!"
        
        # Criar tag latest
        if docker tag "$image_tag:$date_tag" "$image_tag:latest"; then
            print_success "Tag 'latest' criada"
        else
            print_warning "Falha ao criar tag 'latest'"
        fi
        
        # Mostrar informações da imagem
        show_image_info "$image_tag:$date_tag"
        
        # Perguntar se quer fazer push
        if confirm "Deseja fazer push da imagem para o registry?"; then
            push_image "$image_tag" "$date_tag"
        fi
        
        return 0
    else
        print_error "Falha no build da imagem"
        return 1
    fi
}

# Preparar contexto de build
prepare_build_context() {
    local work_dir="$1"
    
    print_step "Preparando contexto de build..."
    
    # Verificar se backup foi executado
    if [[ ! -d "$work_dir/backup" ]]; then
        print_warning "Diretório de backup não encontrado"
        
        if confirm "Deseja executar o script de backup agora?"; then
            if [[ -f "$work_dir/backup.sh" ]]; then
                print_step "Executando backup..."
                cd "$work_dir" && ./backup.sh
                if [[ $? -ne 0 ]]; then
                    print_error "Falha no backup"
                    return 1
                fi
            else
                print_error "Script de backup não encontrado"
                return 1
            fi
        else
            print_error "Backup é necessário para o build"
            return 1
        fi
    fi
    
    # Criar links simbólicos para facilitar o build
    cd "$work_dir"
    
    if [[ -d "backup/data" ]]; then
        ln -sf backup/data data 2>/dev/null || true
    fi
    
    if [[ -d "backup/conf" ]]; then
        ln -sf backup/conf conf 2>/dev/null || true
    fi
    
    # Verificar espaço em disco
    local required_space=2048  # 2GB
    if ! check_disk_space "$work_dir" "$required_space"; then
        return 1
    fi
    
    print_success "Contexto de build preparado"
    return 0
}

# Executar build da imagem Docker
build_docker_image() {
    local work_dir="$1"
    local image_tag="$2"
    local date_tag="$3"
    
    local build_log="$work_dir/build.log"
    local build_start
    build_start=$(date +%s)
    
    print_step "Iniciando build (log: $build_log)..."
    
    # Executar build com timeout
    cd "$work_dir"
    
    if timeout "$BUILD_TIMEOUT" docker build \
        -t "$image_tag:$date_tag" \
        --no-cache \
        --progress=plain \
        . > "$build_log" 2>&1; then
        
        local build_end
        build_end=$(date +%s)
        local build_time=$((build_end - build_start))
        
        print_success "Build concluído em ${build_time}s"
        return 0
    else
        local exit_code=$?
        
        print_error "Build falhou (código: $exit_code)"
        print_info "Últimas linhas do log:"
        tail -20 "$build_log" | while read -r line; do
            echo "  $line"
        done
        
        return 1
    fi
}

# Mostrar informações da imagem
show_image_info() {
    local image="$1"
    
    print_subheader "INFORMAÇÕES DA IMAGEM"
    
    # Tamanho da imagem
    local size
    size=$(docker images "$image" --format "{{.Size}}" | head -1)
    print_info "Tamanho: $size"
    
    # ID da imagem
    local image_id
    image_id=$(docker images "$image" --format "{{.ID}}" | head -1)
    print_info "ID: $image_id"
    
    # Data de criação
    local created
    created=$(docker images "$image" --format "{{.CreatedAt}}" | head -1)
    print_info "Criado: $created"
    
    # Layers da imagem
    local layers
    layers=$(docker history "$image" --format "{{.Size}}" | wc -l)
    print_info "Layers: $layers"
    
    # Verificar se imagem funciona
    print_step "Testando imagem..."
    if docker run --rm "$image" echo "Teste OK" >/dev/null 2>&1; then
        print_success "Imagem testada com sucesso"
    else
        print_warning "Falha no teste da imagem"
    fi
}

# Fazer push da imagem
push_image() {
    local image_tag="$1"
    local date_tag="$2"
    
    print_step "Fazendo push da imagem..."
    
    # Push da versão com data
    if docker push "$image_tag:$date_tag"; then
        print_success "Push da versão $date_tag concluído"
    else
        print_error "Falha no push da versão $date_tag"
        return 1
    fi
    
    # Push da versão latest
    if docker push "$image_tag:latest"; then
        print_success "Push da versão latest concluído"
    else
        print_error "Falha no push da versão latest"
        return 1
    fi
    
    print_success "Push concluído com sucesso!"
    return 0
}

# Limpar imagens antigas
cleanup_old_images() {
    local container_name="$1"
    local keep_count="${2:-5}"
    
    print_step "Limpando imagens antigas..."
    
    # Listar imagens do container
    local images
    images=$(docker images "$DOCKER_REGISTRY/$container_name" --format "{{.Tag}}" | grep -E '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' | sort -r)
    
    local count=0
    while read -r tag; do
        ((count++))
        if [[ "$count" -gt "$keep_count" ]]; then
            print_info "Removendo imagem antiga: $DOCKER_REGISTRY/$container_name:$tag"
            docker rmi "$DOCKER_REGISTRY/$container_name:$tag" 2>/dev/null || true
        fi
    done <<< "$images"
    
    # Limpar imagens dangling
    docker image prune -f >/dev/null 2>&1 || true
    
    print_success "Limpeza concluída"
}

# Verificar registry
check_registry_access() {
    local registry="$1"
    
    print_step "Verificando acesso ao registry: $registry"
    
    # Tentar fazer login (se necessário)
    if ! docker info | grep -q "Registry:"; then
        print_warning "Registry não configurado"
        return 1
    fi
    
    # Testar conectividade
    if ping -c 1 -W 3 "$registry" >/dev/null 2>&1; then
        print_success "Registry acessível"
        return 0
    else
        print_error "Registry não acessível: $registry"
        return 1
    fi
}

# Gerar relatório de build
generate_build_report() {
    local work_dir="$1"
    local container_name="$2"
    local image_tag="$3"
    local date_tag="$4"
    
    local report_file="$work_dir/build_report.txt"
    
    cat > "$report_file" << EOF
# Build Report - $container_name

## Informações Gerais
- Container: $container_name
- Imagem: $image_tag:$date_tag
- Data do Build: $(date)
- Usuário: $(whoami)
- Hostname: $(hostname)

## Arquivos Gerados
- Dockerfile: $(ls -la "$work_dir/Dockerfile" 2>/dev/null || echo "Não encontrado")
- Build Log: $(ls -la "$work_dir/build.log" 2>/dev/null || echo "Não encontrado")
- Backup: $(ls -la "$work_dir/backup" 2>/dev/null || echo "Não encontrado")

## Informações da Imagem
EOF
    
    if docker images "$image_tag:$date_tag" >/dev/null 2>&1; then
        docker images "$image_tag:$date_tag" --format "- Tamanho: {{.Size}}\n- ID: {{.ID}}\n- Criado: {{.CreatedAt}}" >> "$report_file"
    else
        echo "- Imagem não encontrada" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

## Próximos Passos
1. Testar a imagem localmente
2. Fazer deploy no ambiente de homologação
3. Validar funcionamento dos serviços
4. Documentar configurações específicas

## Comandos Úteis
\`\`\`bash
# Executar container localmente
docker run -d --name $container_name-test $image_tag:$date_tag

# Ver logs do container
docker logs $container_name-test

# Acessar container
docker exec -it $container_name-test bash

# Parar e remover container de teste
docker stop $container_name-test && docker rm $container_name-test
\`\`\`
EOF
    
    print_success "Relatório de build gerado: $report_file"
}

# Validar Dockerfile
validate_dockerfile() {
    local dockerfile="$1"
    
    print_step "Validando Dockerfile..."
    
    # Verificações básicas
    if ! grep -q "^FROM " "$dockerfile"; then
        print_error "Dockerfile deve começar com FROM"
        return 1
    fi
    
    if ! grep -q "^CMD \|^ENTRYPOINT " "$dockerfile"; then
        print_warning "Dockerfile não tem CMD ou ENTRYPOINT definido"
    fi
    
    # Verificar se há instruções problemáticas
    if grep -q "^RUN.*rm -rf /" "$dockerfile"; then
        print_error "Comando perigoso detectado no Dockerfile"
        return 1
    fi
    
    # Verificar sintaxe com hadolint se disponível
    if command -v hadolint >/dev/null 2>&1; then
        if hadolint "$dockerfile" >/dev/null 2>&1; then
            print_success "Dockerfile validado com hadolint"
        else
            print_warning "Hadolint encontrou problemas no Dockerfile"
        fi
    fi
    
    print_success "Dockerfile validado"
    return 0
}

# Otimizar build
optimize_build() {
    local work_dir="$1"
    
    print_step "Otimizando contexto de build..."
    
    # Criar .dockerignore se não existir
    local dockerignore="$work_dir/.dockerignore"
    if [[ ! -f "$dockerignore" ]]; then
        cat > "$dockerignore" << EOF
# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Cache
cache/
.cache/

# Git
.git/
.gitignore

# Build artifacts
build.log
build_report.txt

# OS files
.DS_Store
Thumbs.db

# Editor files
*.swp
*.swo
*~
.vscode/
.idea/
EOF
        print_success ".dockerignore criado"
    fi
    
    # Verificar tamanho do contexto
    local context_size
    context_size=$(du -sh "$work_dir" | cut -f1)
    print_info "Tamanho do contexto: $context_size"
    
    if [[ $(du -s "$work_dir" | cut -f1) -gt 1048576 ]]; then  # > 1GB
        print_warning "Contexto de build muito grande ($context_size)"
        print_info "Considere otimizar removendo arquivos desnecessários"
    fi
    
    return 0
}

# Função auxiliar para monitorar progresso do build
monitor_build_progress() {
    local build_log="$1"
    local container_name="$2"
    
    if [[ ! -f "$build_log" ]]; then
        return 1
    fi
    
    # Monitorar em background
    (
        tail -f "$build_log" | while read -r line; do
            case "$line" in
                *"Step "*)
                    print_info "Build: $line"
                    ;;
                *"Successfully built"*)
                    print_success "Build: $line"
                    break
                    ;;
                *"ERROR"*|*"Error"*)
                    print_error "Build: $line"
                    ;;
            esac
        done
    ) &
    
    local monitor_pid=$!
    echo "$monitor_pid"
}

# Verificar recursos do sistema
check_build_resources() {
    print_step "Verificando recursos do sistema..."
    
    # Verificar memória disponível
    local mem_available
    mem_available=$(free -m | awk '/^Mem:/ {print $7}')
    
    if [[ "$mem_available" -lt 1024 ]]; then
        print_warning "Pouca memória disponível: ${mem_available}MB"
        print_info "Recomendado: pelo menos 1GB livre"
    else
        print_success "Memória disponível: ${mem_available}MB"
    fi
    
    # Verificar espaço em disco
    local disk_available
    disk_available=$(df -m . | awk 'NR==2 {print $4}')
    
    if [[ "$disk_available" -lt 2048 ]]; then
        print_warning "Pouco espaço em disco: ${disk_available}MB"
        print_info "Recomendado: pelo menos 2GB livre"
    else
        print_success "Espaço em disco: ${disk_available}MB"
    fi
    
    # Verificar carga do sistema
    local load_avg
    load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ',')
    
    if (( $(echo "$load_avg > 2.0" | bc -l) )); then
        print_warning "Sistema com carga alta: $load_avg"
    else
        print_success "Carga do sistema: $load_avg"
    fi
    
    return 0
}
