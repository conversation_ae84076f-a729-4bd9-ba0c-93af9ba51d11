#!/bin/bash

# ============================================================================
# Dockerfile Generator - Gera Dockerfile baseado nas seleções
# ============================================================================

# Carregar dependências
source "$(dirname "${BASH_SOURCE[0]}")/colors.sh"
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# ============================================================================
# Configurações do gerador
# ============================================================================

# Template base do Dockerfile
DOCKERFILE_TEMPLATE="FROM docker-registry.telemidia.net.br/alma-base:latest

# ============================================================================
# Container gerado automaticamente pelo Container Migration Tool
# Data: {{DATE}}
# Container: {{CONTAINER_NAME}}
# ============================================================================

LABEL maintainer=\"Container Migration Tool\"
LABEL description=\"Migração automática do servidor {{HOSTNAME}}\"
LABEL created=\"{{DATE}}\"
LABEL container.name=\"{{CONTAINER_NAME}}\"

# Definir variáveis de ambiente
ENV CONTAINER_NAME={{CONTAINER_NAME}}
ENV DEBIAN_FRONTEND=noninteractive

{{REPOSITORIES}}

{{PACKAGES}}

{{DIRECTORIES}}

{{CONFIGS}}

{{SERVICES}}

{{PORTS}}

{{VOLUMES}}

{{ENTRYPOINT}}
"

# ============================================================================
# Funções de geração
# ============================================================================

# Função principal para criar Dockerfile
create_dockerfile() {
    local container_name="$1"
    local packages_file="$2"
    local configs_file="$3"
    local directories_file="$4"
    local output_file="$5"
    
    print_header "GERAÇÃO DO DOCKERFILE"
    
    # Verificar arquivos de entrada
    for file in "$packages_file" "$configs_file" "$directories_file"; do
        if [[ ! -f "$file" ]]; then
            print_error "Arquivo não encontrado: $file"
            return 1
        fi
    done
    
    print_step "Gerando Dockerfile para container: $container_name"
    
    # Obter informações do sistema
    local hostname
    hostname=$(hostname)
    local date
    date=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Iniciar com template base
    local dockerfile_content="$DOCKERFILE_TEMPLATE"
    
    # Substituir variáveis básicas
    dockerfile_content="${dockerfile_content//\{\{CONTAINER_NAME\}\}/$container_name}"
    dockerfile_content="${dockerfile_content//\{\{HOSTNAME\}\}/$hostname}"
    dockerfile_content="${dockerfile_content//\{\{DATE\}\}/$date}"
    
    # Gerar seções do Dockerfile
    local repositories_section
    repositories_section=$(generate_repositories_section "$packages_file")
    dockerfile_content="${dockerfile_content//\{\{REPOSITORIES\}\}/$repositories_section}"
    
    local packages_section
    packages_section=$(generate_packages_section "$packages_file")
    dockerfile_content="${dockerfile_content//\{\{PACKAGES\}\}/$packages_section}"
    
    local directories_section
    directories_section=$(generate_directories_section "$directories_file")
    dockerfile_content="${dockerfile_content//\{\{DIRECTORIES\}\}/$directories_section}"
    
    local configs_section
    configs_section=$(generate_configs_section "$configs_file")
    dockerfile_content="${dockerfile_content//\{\{CONFIGS\}\}/$configs_section}"
    
    local services_section
    services_section=$(generate_services_section "$packages_file")
    dockerfile_content="${dockerfile_content//\{\{SERVICES\}\}/$services_section}"
    
    local ports_section
    ports_section=$(generate_ports_section "$packages_file")
    dockerfile_content="${dockerfile_content//\{\{PORTS\}\}/$ports_section}"
    
    local volumes_section
    volumes_section=$(generate_volumes_section "$directories_file")
    dockerfile_content="${dockerfile_content//\{\{VOLUMES\}\}/$volumes_section}"
    
    local entrypoint_section
    entrypoint_section=$(generate_entrypoint_section)
    dockerfile_content="${dockerfile_content//\{\{ENTRYPOINT\}\}/$entrypoint_section}"
    
    # Escrever Dockerfile
    echo "$dockerfile_content" > "$output_file"
    
    # Gerar arquivos auxiliares
    generate_auxiliary_files "$(dirname "$output_file")" "$container_name" "$directories_file" "$configs_file"
    
    print_success "Dockerfile gerado: $output_file"
    return 0
}

# Gerar seção de repositórios
generate_repositories_section() {
    local packages_file="$1"
    local section=""
    
    # Verificar se há pacotes que precisam de repositórios especiais
    local needs_epel=false
    local needs_remi=false
    local needs_powertools=false
    
    if jq -e '.selected_packages[] | select(.name | test("^(php|mysql|redis|nginx)"))' "$packages_file" >/dev/null 2>&1; then
        needs_epel=true
        needs_remi=true
        needs_powertools=true
    fi
    
    if [[ "$needs_powertools" == "true" || "$needs_epel" == "true" || "$needs_remi" == "true" ]]; then
        section+="# ---------------------------------------------------------------\n"
        section+="# Configurar repositórios adicionais\n"
        
        if [[ "$needs_powertools" == "true" ]]; then
            section+="RUN dnf config-manager --set-enabled powertools && \\\\\n"
        fi
        
        if [[ "$needs_epel" == "true" ]]; then
            section+="    dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-8.noarch.rpm && \\\\\n"
        fi
        
        if [[ "$needs_remi" == "true" ]]; then
            section+="    dnf install -y https://rpms.remirepo.net/enterprise/remi-release-8.rpm && \\\\\n"
        fi
        
        section+="    dnf clean all\n\n"
    fi
    
    echo -e "$section"
}

# Gerar seção de pacotes
generate_packages_section() {
    local packages_file="$1"
    local section=""
    
    # Verificar se há pacotes selecionados
    local package_count
    package_count=$(jq '.selected_packages | length' "$packages_file")
    
    if [[ "$package_count" -gt 0 ]]; then
        section+="# ---------------------------------------------------------------\n"
        section+="# Instalar pacotes selecionados\n"
        section+="RUN dnf install -y \\\\\n"
        
        # Agrupar pacotes por categoria para melhor organização
        local categories
        categories=$(jq -r '.selected_packages | group_by(.category) | map(.[0].category) | .[]' "$packages_file" | sort -u)
        
        local first_category=true
        for category in $categories; do
            if [[ "$first_category" != "true" ]]; then
                section+=" \\\\\n"
            fi
            first_category=false
            
            section+="    # $(echo "$category" | tr '[:lower:]' '[:upper:]') packages\n"
            
            local packages
            packages=$(jq -r ".selected_packages | map(select(.category == \"$category\")) | .[].name" "$packages_file")
            
            local first_package=true
            while read -r package; do
                if [[ -n "$package" ]]; then
                    if [[ "$first_package" != "true" ]]; then
                        section+=" \\\\\n"
                    fi
                    first_package=false
                    section+="    $package"
                fi
            done <<< "$packages"
        done
        
        section+=" && \\\\\n    dnf clean all\n\n"
    fi
    
    echo -e "$section"
}

# Gerar seção de diretórios
generate_directories_section() {
    local directories_file="$1"
    local section=""
    
    # Verificar se há diretórios selecionados
    local dir_count
    dir_count=$(jq '.selected_directories | length' "$directories_file")
    
    if [[ "$dir_count" -gt 0 ]]; then
        section+="# ---------------------------------------------------------------\n"
        section+="# Criar estrutura de diretórios\n"
        
        # Criar diretórios
        while read -r path; do
            if [[ -n "$path" ]]; then
                section+="RUN mkdir -p \"$path\"\n"
            fi
        done < <(jq -r '.selected_directories[].path' "$directories_file")
        
        section+="\n# Copiar dados dos diretórios\n"
        
        # Copiar dados
        while read -r path; do
            if [[ -n "$path" ]]; then
                section+="COPY data$path $path/\n"
            fi
        done < <(jq -r '.selected_directories[].path' "$directories_file")
        
        section+="\n"
    fi
    
    echo -e "$section"
}

# Gerar seção de configurações
generate_configs_section() {
    local configs_file="$1"
    local section=""
    
    # Verificar se há configurações selecionadas
    local config_count
    config_count=$(jq '.selected_configs | length' "$configs_file")
    
    if [[ "$config_count" -gt 0 ]]; then
        section+="# ---------------------------------------------------------------\n"
        section+="# Copiar arquivos de configuração\n"
        
        while read -r path; do
            if [[ -n "$path" ]]; then
                local dir
                dir=$(dirname "$path")
                section+="RUN mkdir -p \"$dir\"\n"
                section+="COPY conf$path $path\n"
            fi
        done < <(jq -r '.selected_configs[].path' "$configs_file")
        
        section+="\n"
    fi
    
    echo -e "$section"
}

# Gerar seção de serviços
generate_services_section() {
    local packages_file="$1"
    local section=""
    
    # Mapear pacotes para serviços
    local services=()
    
    while read -r package; do
        case "$package" in
            "httpd"|"apache2")
                services+=("httpd")
                ;;
            "nginx")
                services+=("nginx")
                ;;
            "mysql-server"|"mariadb-server")
                services+=("mysqld")
                ;;
            "postgresql-server")
                services+=("postgresql")
                ;;
            "redis")
                services+=("redis")
                ;;
            "postfix")
                services+=("postfix")
                ;;
            "dovecot")
                services+=("dovecot")
                ;;
            "sshd"|"openssh-server")
                services+=("sshd")
                ;;
        esac
    done < <(jq -r '.selected_packages[].name' "$packages_file" 2>/dev/null || true)
    
    if [[ ${#services[@]} -gt 0 ]]; then
        section+="# ---------------------------------------------------------------\n"
        section+="# Habilitar serviços\n"
        
        for service in "${services[@]}"; do
            section+="RUN systemctl enable $service\n"
        done
        
        section+="\n"
    fi
    
    echo -e "$section"
}

# Gerar seção de portas
generate_ports_section() {
    local packages_file="$1"
    local section=""
    local ports=()
    
    # Mapear pacotes para portas padrão
    while read -r package; do
        case "$package" in
            "httpd"|"apache2"|"nginx")
                ports+=(80 443)
                ;;
            "mysql-server"|"mariadb-server")
                ports+=(3306)
                ;;
            "postgresql-server")
                ports+=(5432)
                ;;
            "redis")
                ports+=(6379)
                ;;
            "postfix")
                ports+=(25 587)
                ;;
            "dovecot")
                ports+=(143 993 110 995)
                ;;
            "sshd"|"openssh-server")
                ports+=(22)
                ;;
        esac
    done < <(jq -r '.selected_packages[].name' "$packages_file" 2>/dev/null || true)
    
    if [[ ${#ports[@]} -gt 0 ]]; then
        section+="# ---------------------------------------------------------------\n"
        section+="# Expor portas\n"
        
        # Remover duplicatas
        local unique_ports
        unique_ports=($(printf '%s\n' "${ports[@]}" | sort -nu))
        
        for port in "${unique_ports[@]}"; do
            section+="EXPOSE $port\n"
        done
        
        section+="\n"
    fi
    
    echo -e "$section"
}

# Gerar seção de volumes
generate_volumes_section() {
    local directories_file="$1"
    local section=""
    local volumes=()
    
    # Identificar diretórios que devem ser volumes
    while read -r path category; do
        case "$category" in
            "database"|"web"|"application")
                volumes+=("$path")
                ;;
        esac
    done < <(jq -r '.selected_directories[] | "\(.path) \(.category)"' "$directories_file" 2>/dev/null || true)
    
    # Adicionar volumes padrão
    volumes+=("/var/log" "/tmp")
    
    if [[ ${#volumes[@]} -gt 0 ]]; then
        section+="# ---------------------------------------------------------------\n"
        section+="# Definir volumes\n"
        section+="VOLUME ["
        
        local first=true
        for volume in "${volumes[@]}"; do
            if [[ "$first" != "true" ]]; then
                section+=", "
            fi
            first=false
            section+="\"$volume\""
        done
        
        section+="]\n\n"
    fi
    
    echo -e "$section"
}

# Gerar seção de entrypoint
generate_entrypoint_section() {
    local section=""
    
    section+="# ---------------------------------------------------------------\n"
    section+="# Script de inicialização\n"
    section+="COPY docker-entrypoint.sh /usr/local/bin/\n"
    section+="RUN chmod +x /usr/local/bin/docker-entrypoint.sh\n\n"
    section+="ENTRYPOINT [\"/usr/local/bin/docker-entrypoint.sh\"]\n"
    section+="CMD [\"/usr/sbin/init\"]\n"
    
    echo -e "$section"
}

# Gerar arquivos auxiliares
generate_auxiliary_files() {
    local output_dir="$1"
    local container_name="$2"
    local directories_file="$3"
    local configs_file="$4"
    
    print_step "Gerando arquivos auxiliares..."
    
    # Gerar script de entrypoint
    generate_entrypoint_script "$output_dir/docker-entrypoint.sh"
    
    # Gerar script de backup
    generate_backup_script "$output_dir/backup.sh" "$directories_file" "$configs_file"
    
    # Gerar docker-compose.yml
    generate_docker_compose "$output_dir/docker-compose.yml" "$container_name" "$directories_file"
    
    # Gerar README
    generate_readme "$output_dir/README.md" "$container_name"
    
    print_success "Arquivos auxiliares gerados"
}

# Gerar script de entrypoint
generate_entrypoint_script() {
    local output_file="$1"
    
    cat > "$output_file" << 'EOF'
#!/bin/bash

# ============================================================================
# Docker Entrypoint Script
# Inicializa serviços e configurações do container
# ============================================================================

set -e

# Função de log
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

log "Iniciando container..."

# Inicializar cron se disponível
if command -v crond >/dev/null 2>&1; then
    log "Iniciando crond..."
    crond
elif command -v cron >/dev/null 2>&1; then
    log "Iniciando cron..."
    cron
fi

# Inicializar rsyslog se disponível
if command -v rsyslogd >/dev/null 2>&1; then
    log "Iniciando rsyslog..."
    rsyslogd
fi

# Ajustar permissões se necessário
if [[ -d /var/www ]]; then
    chown -R apache:apache /var/www 2>/dev/null || true
fi

if [[ -d /var/lib/mysql ]]; then
    chown -R mysql:mysql /var/lib/mysql 2>/dev/null || true
fi

log "Inicialização concluída. Executando comando: $*"

# Executar comando passado como argumento
exec "$@"
EOF
    
    chmod +x "$output_file"
}

# Gerar script de backup
generate_backup_script() {
    local output_file="$1"
    local directories_file="$2"
    local configs_file="$3"
    
    cat > "$output_file" << 'EOF'
#!/bin/bash

# ============================================================================
# Script de Backup
# Faz backup dos dados e configurações selecionados
# ============================================================================

set -e

BACKUP_DIR="./backup"
DATA_DIR="$BACKUP_DIR/data"
CONF_DIR="$BACKUP_DIR/conf"

echo "Criando estrutura de backup..."
mkdir -p "$DATA_DIR" "$CONF_DIR"

echo "=== BACKUP DE DIRETÓRIOS ==="
EOF
    
    # Adicionar comandos de backup para diretórios
    while read -r path; do
        if [[ -n "$path" ]]; then
            cat >> "$output_file" << EOF
if [[ -d "$path" ]]; then
    echo "Fazendo backup de $path..."
    mkdir -p "\$DATA_DIR$path"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \\
          "$path/" "\$DATA_DIR$path/" 2>/dev/null || true
fi
EOF
        fi
    done < <(jq -r '.selected_directories[].path' "$directories_file" 2>/dev/null || true)
    
    cat >> "$output_file" << 'EOF'

echo "=== BACKUP DE CONFIGURAÇÕES ==="
EOF
    
    # Adicionar comandos de backup para configurações
    while read -r path; do
        if [[ -n "$path" ]]; then
            cat >> "$output_file" << EOF
if [[ -f "$path" ]]; then
    echo "Fazendo backup de $path..."
    mkdir -p "\$CONF_DIR$(dirname "$path")"
    cp "$path" "\$CONF_DIR$path" 2>/dev/null || true
fi
EOF
        fi
    done < <(jq -r '.selected_configs[].path' "$configs_file" 2>/dev/null || true)
    
    cat >> "$output_file" << 'EOF'

echo "Backup concluído em: $BACKUP_DIR"
echo "Para usar no Dockerfile, copie os diretórios data/ e conf/ para o contexto do build"
EOF
    
    chmod +x "$output_file"
}

# Gerar docker-compose.yml
generate_docker_compose() {
    local output_file="$1"
    local container_name="$2"
    local directories_file="$3"
    
    cat > "$output_file" << EOF
version: '3.8'

services:
  $container_name:
    build: .
    container_name: $container_name
    hostname: $container_name.pocos-net.com.br
    restart: unless-stopped
    privileged: true
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
EOF
    
    # Adicionar volumes para diretórios selecionados
    while read -r path; do
        if [[ -n "$path" ]]; then
            echo "      - ./$container_name$path:$path" >> "$output_file"
        fi
    done < <(jq -r '.selected_directories[].path' "$directories_file" 2>/dev/null || true)
    
    cat >> "$output_file" << EOF
    networks:
      - docker_ger
      - docker_pub
    command: /usr/sbin/init

networks:
  docker_ger:
    external: true
  docker_pub:
    external: true
EOF
}

# Gerar README
generate_readme() {
    local output_file="$1"
    local container_name="$2"
    
    cat > "$output_file" << EOF
# Container: $container_name

Este container foi gerado automaticamente pelo Container Migration Tool.

## Arquivos gerados

- \`Dockerfile\`: Definição da imagem do container
- \`docker-entrypoint.sh\`: Script de inicialização
- \`backup.sh\`: Script para fazer backup dos dados originais
- \`docker-compose.yml\`: Configuração para Docker Compose
- \`README.md\`: Este arquivo

## Como usar

1. Execute o script de backup no servidor original:
   \`\`\`bash
   ./backup.sh
   \`\`\`

2. Construa a imagem:
   \`\`\`bash
   docker build -t docker-registry.telemidia.net.br/$container_name:latest .
   \`\`\`

3. Execute o container:
   \`\`\`bash
   docker-compose up -d
   \`\`\`

## Estrutura de diretórios

- \`data/\`: Dados das aplicações
- \`conf/\`: Arquivos de configuração

## Notas

- O container usa systemd como init system
- Serviços são habilitados automaticamente
- Volumes são criados para dados persistentes
EOF
}
