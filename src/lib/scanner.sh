#!/bin/bash

# ============================================================================
# System Scanner - Analisa o servidor CentOS
# ============================================================================

# Carregar dependências
source "$(dirname "${BASH_SOURCE[0]}")/colors.sh"
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# ============================================================================
# Configurações do scanner
# ============================================================================

# Pacotes base do CentOS (não incluir no container)
BASE_PACKAGES=(
    "bash" "coreutils" "util-linux" "systemd" "glibc" "yum" "rpm"
    "filesystem" "setup" "basesystem" "tzdata" "rootfiles" "vim-minimal"
    "procps-ng" "kmod" "systemd-libs" "dbus" "dbus-libs" "kernel"
    "initscripts" "chkconfig" "service" "which" "findutils" "grep"
    "sed" "gawk" "tar" "gzip" "bzip2" "xz" "less" "shadow-utils"
    "passwd" "cronie" "logrotate" "rsyslog" "ca-certificates"
    "curl" "wget" "openssh" "openssh-server" "openssh-clients"
    "net-tools" "iproute" "iputils" "bind-utils" "telnet"
    "centos-release" "redhat-release" "system-release" "almalinux-release"
    "rocky-release" "epel-release"
)

# Diretórios de interesse para aplicações
INTERESTING_DIRS=(
    "/opt"
    "/srv" 
    "/var/www"
    "/usr/local/bin"
    "/usr/local/lib"
    "/usr/local/share"
    "/home/<USER>/app"
    "/home/<USER>/apps"
    "/var/lib/mysql"
    "/var/lib/postgresql"
    "/var/lib/mongodb"
    "/var/lib/redis"
    "/etc/httpd"
    "/etc/nginx"
    "/etc/php*"
    "/etc/mysql"
    "/etc/postgresql"
    "/etc/samba"
    "/etc/postfix"
    "/etc/dovecot"
    "/etc/bind"
    "/etc/named"
)

# Arquivos de configuração importantes
IMPORTANT_CONFIGS=(
    "/etc/httpd/conf/httpd.conf"
    "/etc/httpd/conf.d/*.conf"
    "/etc/nginx/nginx.conf"
    "/etc/nginx/conf.d/*.conf"
    "/etc/nginx/sites-available/*"
    "/etc/php.ini"
    "/etc/php*/php.ini"
    "/etc/my.cnf"
    "/etc/mysql/my.cnf"
    "/etc/postgresql/*/main/postgresql.conf"
    "/etc/redis.conf"
    "/etc/redis/redis.conf"
    "/etc/postfix/main.cf"
    "/etc/postfix/master.cf"
    "/etc/dovecot/dovecot.conf"
    "/etc/ssh/sshd_config"
    "/etc/sudoers"
    "/etc/hosts"
    "/etc/resolv.conf"
    "/etc/fstab"
    "/etc/exports"
    "/etc/samba/smb.conf"
    "/etc/bind/named.conf"
    "/etc/named.conf"
    "/etc/firewalld/zones/*.xml"
    "/etc/iptables/rules.v4"
    "/etc/fail2ban/jail.local"
    "/etc/logrotate.d/*"
    "/etc/systemd/system/*.service"
    "/etc/cron.d/*"
    "/etc/crontab"
)

# ============================================================================
# Funções de varredura
# ============================================================================

# Detectar sistema operacional
detect_os() {
    local work_dir="$1"
    local os_info="$work_dir/os_info.json"
    
    print_step "Detectando sistema operacional..."
    
    local os_id=""
    local os_version=""
    local os_name=""
    
    if [[ -f /etc/os-release ]]; then
        os_id=$(awk -F= '/^ID=/{gsub(/"/,"",$2); print $2}' /etc/os-release)
        os_version=$(awk -F= '/^VERSION_ID=/{gsub(/"/,"",$2); print $2}' /etc/os-release)
        os_name=$(awk -F= '/^PRETTY_NAME=/{gsub(/"/,"",$2); print $2}' /etc/os-release)
    fi
    
    # Criar JSON com informações do OS
    cat > "$os_info" << EOF
{
    "id": "$os_id",
    "version": "$os_version", 
    "name": "$os_name",
    "hostname": "$(hostname)",
    "kernel": "$(uname -r)",
    "architecture": "$(uname -m)",
    "scan_date": "$(date -Iseconds)"
}
EOF
    
    print_success "Sistema detectado: $os_name"
    return 0
}

# Varrer pacotes instalados
scan_packages() {
    local work_dir="$1"
    local packages_file="$work_dir/packages.json"
    local custom_packages_file="$work_dir/custom_packages.json"
    
    print_step "Analisando pacotes instalados..."
    
    if ! command -v rpm >/dev/null 2>&1; then
        print_error "Sistema não baseado em RPM"
        return 1
    fi
    
    # Obter lista completa de pacotes
    local total_packages
    total_packages=$(rpm -qa --qf "%{NAME}|%{VERSION}|%{RELEASE}|%{ARCH}|%{SIZE}|%{SUMMARY}\n" | wc -l)
    
    print_info "Total de pacotes instalados: $total_packages"
    
    # Criar arquivo JSON com todos os pacotes
    echo '{"packages": [' > "$packages_file"
    local first=true
    
    rpm -qa --qf "%{NAME}|%{VERSION}|%{RELEASE}|%{ARCH}|%{SIZE}|%{SUMMARY}\n" | while IFS='|' read -r name version release arch size summary; do
        if [[ "$first" == "true" ]]; then
            first=false
        else
            echo "," >> "$packages_file"
        fi
        
        # Escapar aspas no summary
        summary=$(echo "$summary" | sed 's/"/\\"/g')
        
        cat >> "$packages_file" << EOF
    {
        "name": "$name",
        "version": "$version",
        "release": "$release", 
        "arch": "$arch",
        "size": $size,
        "summary": "$summary"
    }
EOF
    done
    
    echo ']}' >> "$packages_file"
    
    # Identificar pacotes customizados (não base)
    print_step "Identificando pacotes customizados..."
    
    echo '{"custom_packages": [' > "$custom_packages_file"
    first=true
    local custom_count=0
    
    while IFS='|' read -r name version release arch size summary; do
        # Verificar se não é um pacote base
        local is_base=false
        for base_pkg in "${BASE_PACKAGES[@]}"; do
            if [[ "$name" == "$base_pkg"* ]]; then
                is_base=true
                break
            fi
        done
        
        # Se não é base e parece interessante, incluir
        if [[ "$is_base" == "false" ]] && is_interesting_package "$name"; then
            if [[ "$first" == "true" ]]; then
                first=false
            else
                echo "," >> "$custom_packages_file"
            fi
            
            summary=$(echo "$summary" | sed 's/"/\\"/g')
            
            cat >> "$custom_packages_file" << EOF
        {
            "name": "$name",
            "version": "$version",
            "release": "$release",
            "arch": "$arch", 
            "size": $size,
            "summary": "$summary",
            "category": "$(categorize_package "$name")"
        }
EOF
            ((custom_count++))
        fi
    done < <(rpm -qa --qf "%{NAME}|%{VERSION}|%{RELEASE}|%{ARCH}|%{SIZE}|%{SUMMARY}\n")
    
    echo ']}' >> "$custom_packages_file"
    
    print_success "Pacotes customizados encontrados: $custom_count"
    return 0
}

# Verificar se pacote é interessante
is_interesting_package() {
    local package="$1"
    
    # Padrões de pacotes interessantes
    local interesting_patterns=(
        "^(httpd|nginx|apache)"
        "^(mysql|mariadb|postgresql|mongodb|redis)"
        "^(php|python|nodejs|java|ruby|perl)"
        "^(git|subversion|mercurial)"
        "^(docker|kubernetes|podman)"
        "^(postfix|dovecot|sendmail)"
        "^(bind|named|dnsmasq)"
        "^(samba|nfs-utils|vsftpd)"
        "^(fail2ban|iptables|firewalld)"
        "^(certbot|letsencrypt)"
        "^(vim|emacs|nano)"
        "^(htop|tmux|screen)"
        "^(zsh|fish)"
        "^(gcc|make|cmake|autoconf)"
        "^(rpm-build|mock|createrepo)"
        "^(epel-release|remi-release|ius-release)"
    )
    
    for pattern in "${interesting_patterns[@]}"; do
        if [[ "$package" =~ $pattern ]]; then
            return 0
        fi
    done
    
    return 1
}

# Categorizar pacote
categorize_package() {
    local package="$1"
    
    if [[ "$package" =~ ^(httpd|nginx|apache) ]]; then
        echo "webserver"
    elif [[ "$package" =~ ^(mysql|mariadb|postgresql|mongodb|redis) ]]; then
        echo "database"
    elif [[ "$package" =~ ^(php|python|nodejs|java|ruby|perl) ]]; then
        echo "runtime"
    elif [[ "$package" =~ ^(git|subversion|mercurial) ]]; then
        echo "vcs"
    elif [[ "$package" =~ ^(docker|kubernetes|podman) ]]; then
        echo "container"
    elif [[ "$package" =~ ^(postfix|dovecot|sendmail) ]]; then
        echo "mail"
    elif [[ "$package" =~ ^(bind|named|dnsmasq) ]]; then
        echo "dns"
    elif [[ "$package" =~ ^(samba|nfs-utils|vsftpd) ]]; then
        echo "fileserver"
    elif [[ "$package" =~ ^(fail2ban|iptables|firewalld) ]]; then
        echo "security"
    elif [[ "$package" =~ ^(gcc|make|cmake|autoconf) ]]; then
        echo "development"
    else
        echo "other"
    fi
}

# Varrer serviços habilitados
scan_services() {
    local work_dir="$1"
    local services_file="$work_dir/services.json"
    
    print_step "Analisando serviços habilitados..."
    
    echo '{"services": [' > "$services_file"
    local first=true
    local service_count=0
    
    if command -v systemctl >/dev/null 2>&1; then
        while read -r service state; do
            if [[ "$first" == "true" ]]; then
                first=false
            else
                echo "," >> "$services_file"
            fi
            
            # Obter informações adicionais do serviço
            local description=""
            description=$(systemctl show "$service" --property=Description --value 2>/dev/null || echo "")
            description=$(echo "$description" | sed 's/"/\\"/g')
            
            cat >> "$services_file" << EOF
        {
            "name": "$service",
            "state": "$state",
            "description": "$description"
        }
EOF
            ((service_count++))
        done < <(systemctl list-unit-files --type=service --state=enabled --no-pager --no-legend | awk '{print $1, $2}')
    fi
    
    echo ']}' >> "$services_file"
    
    print_success "Serviços habilitados encontrados: $service_count"
    return 0
}

# Varrer diretórios de interesse
scan_directories() {
    local work_dir="$1"
    local directories_file="$work_dir/directories.json"
    
    print_step "Analisando diretórios de aplicação..."
    
    echo '{"directories": [' > "$directories_file"
    local first=true
    local dir_count=0
    
    for pattern in "${INTERESTING_DIRS[@]}"; do
        for dir in $pattern; do
            if [[ -d "$dir" ]]; then
                # Verificar se tem conteúdo relevante
                local file_count
                file_count=$(find "$dir" -type f 2>/dev/null | wc -l)
                
                if [[ "$file_count" -gt 0 ]]; then
                    if [[ "$first" == "true" ]]; then
                        first=false
                    else
                        echo "," >> "$directories_file"
                    fi
                    
                    local size_bytes
                    size_bytes=$(du -sb "$dir" 2>/dev/null | cut -f1 || echo 0)
                    local size_human
                    size_human=$(bytes_to_human "$size_bytes")
                    
                    cat >> "$directories_file" << EOF
                {
                    "path": "$dir",
                    "size_bytes": $size_bytes,
                    "size_human": "$size_human",
                    "file_count": $file_count,
                    "category": "$(categorize_directory "$dir")"
                }
EOF
                    ((dir_count++))
                fi
            fi
        done
    done
    
    echo ']}' >> "$directories_file"
    
    print_success "Diretórios de interesse encontrados: $dir_count"
    return 0
}

# Categorizar diretório
categorize_directory() {
    local dir="$1"
    
    case "$dir" in
        "/var/www"*|"/srv"*)
            echo "web"
            ;;
        "/var/lib/mysql"*|"/var/lib/postgresql"*|"/var/lib/mongodb"*)
            echo "database"
            ;;
        "/opt"*)
            echo "application"
            ;;
        "/usr/local"*)
            echo "local"
            ;;
        "/home"*)
            echo "user"
            ;;
        "/etc"*)
            echo "config"
            ;;
        *)
            echo "other"
            ;;
    esac
}

# Varrer arquivos de configuração
scan_configs() {
    local work_dir="$1"
    local configs_file="$work_dir/configs.json"
    
    print_step "Analisando arquivos de configuração..."
    
    echo '{"configs": [' > "$configs_file"
    local first=true
    local config_count=0
    
    for pattern in "${IMPORTANT_CONFIGS[@]}"; do
        for config in $pattern; do
            if [[ -f "$config" ]]; then
                if [[ "$first" == "true" ]]; then
                    first=false
                else
                    echo "," >> "$configs_file"
                fi
                
                local size_bytes
                size_bytes=$(stat -c%s "$config" 2>/dev/null || echo 0)
                local modified
                modified=$(stat -c%Y "$config" 2>/dev/null || echo 0)
                local modified_human
                modified_human=$(date -d "@$modified" '+%Y-%m-%d %H:%M:%S' 2>/dev/null || echo "unknown")
                
                cat >> "$configs_file" << EOF
            {
                "path": "$config",
                "size_bytes": $size_bytes,
                "modified_timestamp": $modified,
                "modified_human": "$modified_human",
                "category": "$(categorize_config "$config")"
            }
EOF
                ((config_count++))
            fi
        done
    done
    
    echo ']}' >> "$configs_file"
    
    print_success "Arquivos de configuração encontrados: $config_count"
    return 0
}

# Categorizar arquivo de configuração
categorize_config() {
    local config="$1"
    
    case "$config" in
        *"httpd"*|*"apache"*)
            echo "webserver"
            ;;
        *"nginx"*)
            echo "webserver"
            ;;
        *"php"*)
            echo "php"
            ;;
        *"mysql"*|*"mariadb"*)
            echo "database"
            ;;
        *"postgresql"*)
            echo "database"
            ;;
        *"redis"*)
            echo "cache"
            ;;
        *"postfix"*|*"dovecot"*)
            echo "mail"
            ;;
        *"ssh"*)
            echo "ssh"
            ;;
        *"firewall"*|*"iptables"*)
            echo "security"
            ;;
        *"cron"*)
            echo "cron"
            ;;
        *)
            echo "system"
            ;;
    esac
}

# Varrer portas em uso
scan_ports() {
    local work_dir="$1"
    local ports_file="$work_dir/ports.json"
    
    print_step "Analisando portas em uso..."
    
    echo '{"ports": [' > "$ports_file"
    local first=true
    local port_count=0
    
    if command -v ss >/dev/null 2>&1; then
        while read -r proto local_addr state process; do
            local port
            port=$(echo "$local_addr" | sed 's/.*://')
            
            # Filtrar portas numéricas válidas
            if [[ "$port" =~ ^[0-9]+$ ]] && [[ "$port" -gt 0 ]] && [[ "$port" -lt 65536 ]]; then
                if [[ "$first" == "true" ]]; then
                    first=false
                else
                    echo "," >> "$ports_file"
                fi
                
                cat >> "$ports_file" << EOF
            {
                "port": $port,
                "protocol": "$proto",
                "address": "$local_addr",
                "state": "$state",
                "process": "$process"
            }
EOF
                ((port_count++))
            fi
        done < <(ss -tuln | awk 'NR>1 {print $1, $5, $2, $7}')
    fi
    
    echo ']}' >> "$ports_file"
    
    print_success "Portas em uso encontradas: $port_count"
    return 0
}

# Função principal de varredura
scan_system() {
    local work_dir="$1"
    
    print_header "VARREDURA DO SISTEMA"
    
    # Verificar se é root
    if ! check_root; then
        return 1
    fi
    
    # Criar diretório de trabalho
    mkdir -p "$work_dir"
    
    # Executar varreduras
    detect_os "$work_dir" || return 1
    scan_packages "$work_dir" || return 1
    scan_services "$work_dir" || return 1
    scan_directories "$work_dir" || return 1
    scan_configs "$work_dir" || return 1
    scan_ports "$work_dir" || return 1
    
    # Consolidar resultados
    consolidate_scan_results "$work_dir"
    
    print_success "Varredura do sistema concluída"
    return 0
}

# Consolidar resultados da varredura
consolidate_scan_results() {
    local work_dir="$1"
    local results_file="$work_dir/scan_results.json"

    print_step "Consolidando resultados..."

    # Criar arquivo JSON consolidado
    {
        echo "{"
        echo "    \"scan_info\": {"
        echo "        \"version\": \"1.0\","
        echo "        \"timestamp\": \"$(date -Iseconds)\","
        echo "        \"hostname\": \"$(hostname)\""
        echo "    },"

        echo -n "    \"os_info\": "
        cat "$work_dir/os_info.json" 2>/dev/null || echo '{}'
        echo ","

        echo -n "    \"packages\": "
        cat "$work_dir/packages.json" 2>/dev/null || echo '{"packages":[]}'
        echo ","

        echo -n "    \"custom_packages\": "
        cat "$work_dir/custom_packages.json" 2>/dev/null || echo '{"custom_packages":[]}'
        echo ","

        echo -n "    \"services\": "
        cat "$work_dir/services.json" 2>/dev/null || echo '{"services":[]}'
        echo ","

        echo -n "    \"directories\": "
        cat "$work_dir/directories.json" 2>/dev/null || echo '{"directories":[]}'
        echo ","

        echo -n "    \"configs\": "
        cat "$work_dir/configs.json" 2>/dev/null || echo '{"configs":[]}'
        echo ","

        echo -n "    \"ports\": "
        cat "$work_dir/ports.json" 2>/dev/null || echo '{"ports":[]}'
        echo
        echo "}"
    } > "$results_file"

    print_success "Resultados consolidados em: $results_file"
}

# Mostrar resumo da varredura
show_scan_summary() {
    local results_file="$1"
    
    if [[ ! -f "$results_file" ]]; then
        print_error "Arquivo de resultados não encontrado: $results_file"
        return 1
    fi
    
    print_subheader "RESUMO DA VARREDURA"
    
    local os_name
    os_name=$(jq -r '.os_info.name // "Desconhecido"' "$results_file")
    print_info "Sistema: $os_name"
    
    local total_packages
    total_packages=$(jq '.packages.packages | length' "$results_file")
    print_info "Total de pacotes: $total_packages"
    
    local custom_packages
    custom_packages=$(jq '.custom_packages.custom_packages | length' "$results_file")
    print_info "Pacotes customizados: $custom_packages"
    
    local services
    services=$(jq '.services.services | length' "$results_file")
    print_info "Serviços habilitados: $services"
    
    local directories
    directories=$(jq '.directories.directories | length' "$results_file")
    print_info "Diretórios de interesse: $directories"
    
    local configs
    configs=$(jq '.configs.configs | length' "$results_file")
    print_info "Arquivos de configuração: $configs"
    
    local ports
    ports=$(jq '.ports.ports | length' "$results_file")
    print_info "Portas em uso: $ports"
}
