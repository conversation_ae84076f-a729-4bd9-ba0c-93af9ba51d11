#!/bin/bash

# ============================================================================
# Component Selector - Interface para seleção de componentes
# ============================================================================

# Carregar dependências
source "$(dirname "${BASH_SOURCE[0]}")/colors.sh"
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# ============================================================================
# Funções de seleção
# ============================================================================

# Seleção de pacotes
select_packages() {
    local scan_results="$1"
    local output_file="$2"
    
    print_header "SELEÇÃO DE PACOTES"
    
    if [[ ! -f "$scan_results" ]]; then
        print_error "Arquivo de resultados não encontrado: $scan_results"
        return 1
    fi
    
    # Obter pacotes customizados por categoria
    local categories
    categories=$(jq -r '.custom_packages.custom_packages | group_by(.category) | map(.[0].category) | .[]' "$scan_results" | sort -u)
    
    if [[ -z "$categories" ]]; then
        print_warning "Nenhum pacote customizado encontrado"
        echo '{"selected_packages": []}' > "$output_file"
        return 0
    fi
    
    echo '{"selected_packages": [' > "$output_file"
    local first=true
    local total_selected=0
    
    for category in $categories; do
        print_subheader "CATEGORIA: $(echo "$category" | tr '[:lower:]' '[:upper:]')"
        
        # Obter pacotes da categoria
        local packages
        packages=$(jq -r ".custom_packages.custom_packages | map(select(.category == \"$category\")) | .[] | \"\(.name)|\(.version)|\(.summary)\"" "$scan_results")
        
        if [[ -z "$packages" ]]; then
            continue
        fi
        
        echo "Pacotes disponíveis nesta categoria:"
        echo
        
        local package_array=()
        local i=1
        
        while IFS='|' read -r name version summary; do
            printf "%2d. %-30s %s\n" "$i" "$name-$version" "$summary"
            package_array+=("$name|$version|$summary")
            ((i++))
        done <<< "$packages"
        
        echo
        echo "Opções:"
        echo "a) Selecionar todos"
        echo "n) Não selecionar nenhum"
        echo "s) Selecionar específicos (ex: 1,3,5-7)"
        echo "q) Pular categoria"
        echo
        
        while true; do
            echo -n "Sua escolha: "
            read -r choice
            
            case "$choice" in
                "a"|"A")
                    # Selecionar todos
                    for pkg_info in "${package_array[@]}"; do
                        IFS='|' read -r name version summary <<< "$pkg_info"
                        if [[ "$first" != "true" ]]; then
                            echo "," >> "$output_file"
                        fi
                        first=false

                        # Escapar aspas no summary
                        summary=$(echo "$summary" | sed 's/"/\\"/g')

                        cat >> "$output_file" << EOF
        {
            "name": "$name",
            "version": "$version",
            "summary": "$summary",
            "category": "$category"
        }
EOF
                        ((total_selected++))
                    done
                    print_success "Todos os pacotes da categoria $category selecionados"
                    break
                    ;;
                "n"|"N")
                    # Não selecionar nenhum
                    print_info "Nenhum pacote da categoria $category selecionado"
                    break
                    ;;
                "q"|"Q")
                    # Pular categoria
                    print_info "Categoria $category pulada"
                    break
                    ;;
                "s"|"S")
                    # Seleção específica
                    echo -n "Digite os números (ex: 1,3,5-7): "
                    read -r selection
                    
                    if select_specific_packages "$selection" "${package_array[@]}"; then
                        local selected_indices
                        selected_indices=$(parse_selection "$selection" "${#package_array[@]}")
                        
                        for idx in $selected_indices; do
                            local pkg_info="${package_array[$((idx-1))]}"
                            IFS='|' read -r name version summary <<< "$pkg_info"

                            if [[ "$first" != "true" ]]; then
                                echo "," >> "$output_file"
                            fi
                            first=false

                            # Escapar aspas no summary
                            summary=$(echo "$summary" | sed 's/"/\\"/g')

                            cat >> "$output_file" << EOF
        {
            "name": "$name",
            "version": "$version",
            "summary": "$summary",
            "category": "$category"
        }
EOF
                            ((total_selected++))
                        done
                        
                        print_success "Pacotes específicos da categoria $category selecionados"
                        break
                    else
                        print_error "Seleção inválida, tente novamente"
                    fi
                    ;;
                *)
                    print_error "Opção inválida"
                    ;;
            esac
        done
        
        echo
    done
    
    echo ']}' >> "$output_file"
    
    print_success "Seleção de pacotes concluída. Total selecionado: $total_selected"
    return 0
}



# Seleção de configurações
select_configs() {
    local scan_results="$1"
    local output_file="$2"
    
    print_header "SELEÇÃO DE CONFIGURAÇÕES"
    
    if [[ ! -f "$scan_results" ]]; then
        print_error "Arquivo de resultados não encontrado: $scan_results"
        return 1
    fi
    
    # Obter configurações por categoria
    local categories
    categories=$(jq -r '.configs.configs | group_by(.category) | map(.[0].category) | .[]' "$scan_results" | sort -u)
    
    if [[ -z "$categories" ]]; then
        print_warning "Nenhuma configuração encontrada"
        echo '{"selected_configs": []}' > "$output_file"
        return 0
    fi
    
    echo '{"selected_configs": [' > "$output_file"
    local first=true
    local total_selected=0
    
    for category in $categories; do
        print_subheader "CATEGORIA: $(echo "$category" | tr '[:lower:]' '[:upper:]')"
        
        # Obter configurações da categoria
        local configs
        configs=$(jq -r ".configs.configs | map(select(.category == \"$category\")) | .[] | \"\(.path)|\(.size_bytes)|\(.modified_human)\"" "$scan_results")
        
        if [[ -z "$configs" ]]; then
            continue
        fi
        
        echo "Configurações disponíveis nesta categoria:"
        echo
        
        local config_array=()
        local i=1
        
        while IFS='|' read -r path size_bytes modified; do
            local size_human
            size_human=$(bytes_to_human "$size_bytes")
            printf "%2d. %-50s %8s %s\n" "$i" "$path" "$size_human" "$modified"
            config_array+=("$path|$size_bytes|$modified")
            ((i++))
        done <<< "$configs"
        
        echo
        echo "Opções:"
        echo "a) Selecionar todos"
        echo "n) Não selecionar nenhum"
        echo "s) Selecionar específicos (ex: 1,3,5-7)"
        echo "q) Pular categoria"
        echo
        
        while true; do
            echo -n "Sua escolha: "
            read -r choice
            
            case "$choice" in
                "a"|"A")
                    # Selecionar todos
                    for config_info in "${config_array[@]}"; do
                        IFS='|' read -r path size_bytes modified <<< "$config_info"
                        add_selected_config "$output_file" "$path" "$size_bytes" "$modified" "$category" first
                        first=false
                        ((total_selected++))
                    done
                    print_success "Todas as configurações da categoria $category selecionadas"
                    break
                    ;;
                "n"|"N")
                    # Não selecionar nenhum
                    print_info "Nenhuma configuração da categoria $category selecionada"
                    break
                    ;;
                "q"|"Q")
                    # Pular categoria
                    print_info "Categoria $category pulada"
                    break
                    ;;
                "s"|"S")
                    # Seleção específica
                    echo -n "Digite os números (ex: 1,3,5-7): "
                    read -r selection
                    
                    if select_specific_configs "$selection" "${config_array[@]}"; then
                        local selected_indices
                        selected_indices=$(parse_selection "$selection" "${#config_array[@]}")
                        
                        for idx in $selected_indices; do
                            local config_info="${config_array[$((idx-1))]}"
                            IFS='|' read -r path size_bytes modified <<< "$config_info"
                            add_selected_config "$output_file" "$path" "$size_bytes" "$modified" "$category" first
                            first=false
                            ((total_selected++))
                        done
                        
                        print_success "Configurações específicas da categoria $category selecionadas"
                        break
                    else
                        print_error "Seleção inválida, tente novamente"
                    fi
                    ;;
                *)
                    print_error "Opção inválida"
                    ;;
            esac
        done
        
        echo
    done
    
    echo ']}' >> "$output_file"
    
    print_success "Seleção de configurações concluída. Total selecionado: $total_selected"
    return 0
}

# Adicionar configuração selecionada ao arquivo JSON
add_selected_config() {
    local output_file="$1"
    local path="$2"
    local size_bytes="$3"
    local modified="$4"
    local category="$5"
    local is_first="$6"
    
    if [[ "$is_first" != "true" ]]; then
        echo "," >> "$output_file"
    fi
    
    cat >> "$output_file" << EOF
        {
            "path": "$path",
            "size_bytes": $size_bytes,
            "modified": "$modified",
            "category": "$category"
        }
EOF
}

# Seleção de diretórios
select_directories() {
    local scan_results="$1"
    local output_file="$2"
    
    print_header "SELEÇÃO DE DIRETÓRIOS"
    
    if [[ ! -f "$scan_results" ]]; then
        print_error "Arquivo de resultados não encontrado: $scan_results"
        return 1
    fi
    
    # Obter diretórios por categoria
    local categories
    categories=$(jq -r '.directories.directories | group_by(.category) | map(.[0].category) | .[]' "$scan_results" | sort -u)
    
    if [[ -z "$categories" ]]; then
        print_warning "Nenhum diretório encontrado"
        echo '{"selected_directories": []}' > "$output_file"
        return 0
    fi
    
    echo '{"selected_directories": [' > "$output_file"
    local first=true
    local total_selected=0
    
    for category in $categories; do
        print_subheader "CATEGORIA: $(echo "$category" | tr '[:lower:]' '[:upper:]')"
        
        # Obter diretórios da categoria
        local directories
        directories=$(jq -r ".directories.directories | map(select(.category == \"$category\")) | .[] | \"\(.path)|\(.size_bytes)|\(.file_count)|\(.size_human)\"" "$scan_results")
        
        if [[ -z "$directories" ]]; then
            continue
        fi
        
        echo "Diretórios disponíveis nesta categoria:"
        echo
        
        local dir_array=()
        local i=1
        
        while IFS='|' read -r path size_bytes file_count size_human; do
            printf "%2d. %-40s %8s (%s arquivos)\n" "$i" "$path" "$size_human" "$file_count"
            dir_array+=("$path|$size_bytes|$file_count|$size_human")
            ((i++))
        done <<< "$directories"
        
        echo
        echo "Opções:"
        echo "a) Selecionar todos"
        echo "n) Não selecionar nenhum"
        echo "s) Selecionar específicos (ex: 1,3,5-7)"
        echo "q) Pular categoria"
        echo
        
        while true; do
            echo -n "Sua escolha: "
            read -r choice
            
            case "$choice" in
                "a"|"A")
                    # Selecionar todos
                    for dir_info in "${dir_array[@]}"; do
                        IFS='|' read -r path size_bytes file_count size_human <<< "$dir_info"
                        add_selected_directory "$output_file" "$path" "$size_bytes" "$file_count" "$size_human" "$category" first
                        first=false
                        ((total_selected++))
                    done
                    print_success "Todos os diretórios da categoria $category selecionados"
                    break
                    ;;
                "n"|"N")
                    # Não selecionar nenhum
                    print_info "Nenhum diretório da categoria $category selecionado"
                    break
                    ;;
                "q"|"Q")
                    # Pular categoria
                    print_info "Categoria $category pulada"
                    break
                    ;;
                "s"|"S")
                    # Seleção específica
                    echo -n "Digite os números (ex: 1,3,5-7): "
                    read -r selection
                    
                    if select_specific_directories "$selection" "${dir_array[@]}"; then
                        local selected_indices
                        selected_indices=$(parse_selection "$selection" "${#dir_array[@]}")
                        
                        for idx in $selected_indices; do
                            local dir_info="${dir_array[$((idx-1))]}"
                            IFS='|' read -r path size_bytes file_count size_human <<< "$dir_info"
                            add_selected_directory "$output_file" "$path" "$size_bytes" "$file_count" "$size_human" "$category" first
                            first=false
                            ((total_selected++))
                        done
                        
                        print_success "Diretórios específicos da categoria $category selecionados"
                        break
                    else
                        print_error "Seleção inválida, tente novamente"
                    fi
                    ;;
                *)
                    print_error "Opção inválida"
                    ;;
            esac
        done
        
        echo
    done
    
    echo ']}' >> "$output_file"
    
    print_success "Seleção de diretórios concluída. Total selecionado: $total_selected"
    return 0
}

# Adicionar diretório selecionado ao arquivo JSON
add_selected_directory() {
    local output_file="$1"
    local path="$2"
    local size_bytes="$3"
    local file_count="$4"
    local size_human="$5"
    local category="$6"
    local is_first="$7"
    
    if [[ "$is_first" != "true" ]]; then
        echo "," >> "$output_file"
    fi
    
    cat >> "$output_file" << EOF
        {
            "path": "$path",
            "size_bytes": $size_bytes,
            "file_count": $file_count,
            "size_human": "$size_human",
            "category": "$category"
        }
EOF
}

# Funções auxiliares para seleção específica
select_specific_packages() {
    local selection="$1"
    shift
    local packages=("$@")
    
    # Validar seleção
    parse_selection "$selection" "${#packages[@]}" >/dev/null
}

select_specific_configs() {
    local selection="$1"
    shift
    local configs=("$@")
    
    # Validar seleção
    parse_selection "$selection" "${#configs[@]}" >/dev/null
}

select_specific_directories() {
    local selection="$1"
    shift
    local directories=("$@")
    
    # Validar seleção
    parse_selection "$selection" "${#directories[@]}" >/dev/null
}

# Parsear seleção (ex: 1,3,5-7 -> 1 3 5 6 7)
parse_selection() {
    local selection="$1"
    local max_items="$2"
    local result=()
    
    # Dividir por vírgulas
    IFS=',' read -ra parts <<< "$selection"
    
    for part in "${parts[@]}"; do
        part=$(echo "$part" | tr -d ' ')  # Remover espaços
        
        if [[ "$part" =~ ^[0-9]+$ ]]; then
            # Número simples
            if [[ "$part" -ge 1 && "$part" -le "$max_items" ]]; then
                result+=("$part")
            else
                return 1
            fi
        elif [[ "$part" =~ ^[0-9]+-[0-9]+$ ]]; then
            # Faixa (ex: 5-7)
            local start="${part%-*}"
            local end="${part#*-}"
            
            if [[ "$start" -ge 1 && "$end" -le "$max_items" && "$start" -le "$end" ]]; then
                for ((i=start; i<=end; i++)); do
                    result+=("$i")
                done
            else
                return 1
            fi
        else
            return 1
        fi
    done
    
    # Remover duplicatas e ordenar
    printf '%s\n' "${result[@]}" | sort -nu | tr '\n' ' '
    return 0
}
