#!/bin/bash

# ============================================================================
# Colors and formatting definitions
# ============================================================================

# Verificar se o terminal suporta cores
if [[ -t 1 ]] && command -v tput >/dev/null 2>&1 && tput colors >/dev/null 2>&1; then
    # Cores básicas
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[0;33m'
    BLUE='\033[0;34m'
    MAGENTA='\033[0;35m'
    CYAN='\033[0;36m'
    WHITE='\033[0;37m'
    
    # Cores em negrito
    BOLD_RED='\033[1;31m'
    BOLD_GREEN='\033[1;32m'
    BOLD_YELLOW='\033[1;33m'
    BOLD_BLUE='\033[1;34m'
    BOLD_MAGENTA='\033[1;35m'
    BOLD_CYAN='\033[1;36m'
    BOLD_WHITE='\033[1;37m'
    
    # Formatação
    BOLD='\033[1m'
    DIM='\033[2m'
    UNDERLINE='\033[4m'
    BLINK='\033[5m'
    REVERSE='\033[7m'
    
    # Reset
    NC='\033[0m' # No Color
else
    # Terminal não suporta cores - usar strings vazias
    RED=''
    GREEN=''
    YELLOW=''
    BLUE=''
    MAGENTA=''
    CYAN=''
    WHITE=''
    BOLD_RED=''
    BOLD_GREEN=''
    BOLD_YELLOW=''
    BOLD_BLUE=''
    BOLD_MAGENTA=''
    BOLD_CYAN=''
    BOLD_WHITE=''
    BOLD=''
    DIM=''
    UNDERLINE=''
    BLINK=''
    REVERSE=''
    NC=''
fi

# ============================================================================
# Funções de formatação
# ============================================================================

# Função para imprimir texto colorido
print_color() {
    local color="$1"
    local text="$2"
    echo -e "${color}${text}${NC}"
}

# Funções de conveniência
print_error() {
    print_color "$RED" "❌ ERRO: $1" >&2
}

print_warning() {
    print_color "$YELLOW" "⚠️  AVISO: $1"
}

print_success() {
    print_color "$GREEN" "✅ SUCESSO: $1"
}

print_info() {
    print_color "$BLUE" "ℹ️  INFO: $1"
}

print_step() {
    print_color "$CYAN" "🔄 $1"
}

print_header() {
    echo
    print_color "$BOLD_BLUE" "═══════════════════════════════════════════════════════════════════════════════"
    print_color "$BOLD_BLUE" "  $1"
    print_color "$BOLD_BLUE" "═══════════════════════════════════════════════════════════════════════════════"
    echo
}

print_subheader() {
    echo
    print_color "$BOLD_CYAN" "─── $1 ───"
    echo
}

# Função para criar barras de progresso simples
print_progress() {
    local current="$1"
    local total="$2"
    local description="$3"
    local width=50
    
    local percentage=$((current * 100 / total))
    local filled=$((current * width / total))
    local empty=$((width - filled))
    
    printf "\r${CYAN}%s [" "$description"
    printf "%*s" "$filled" | tr ' ' '█'
    printf "%*s" "$empty" | tr ' ' '░'
    printf "] %d%% (%d/%d)${NC}" "$percentage" "$current" "$total"
    
    if [[ "$current" -eq "$total" ]]; then
        echo
    fi
}

# Função para criar spinner de loading
show_spinner() {
    local pid=$1
    local message="$2"
    local delay=0.1
    local spinstr='|/-\'
    
    while kill -0 "$pid" 2>/dev/null; do
        local temp=${spinstr#?}
        printf "\r${CYAN}%s %c${NC}" "$message" "$spinstr"
        spinstr=$temp${spinstr%"$temp"}
        sleep $delay
    done
    printf "\r%*s\r" $((${#message} + 2)) ""
}

# Função para criar tabelas simples
print_table_header() {
    local -a headers=("$@")
    local separator=""
    
    # Imprimir cabeçalho
    printf "${BOLD_BLUE}"
    for header in "${headers[@]}"; do
        printf "%-20s " "$header"
        separator+="────────────────────── "
    done
    printf "${NC}\n"
    
    # Imprimir separador
    printf "${BLUE}%s${NC}\n" "$separator"
}

print_table_row() {
    local -a columns=("$@")
    
    for column in "${columns[@]}"; do
        printf "%-20s " "$column"
    done
    printf "\n"
}

# Função para criar caixas de texto
print_box() {
    local text="$1"
    local color="${2:-$BLUE}"
    local width=80
    
    # Linha superior
    printf "${color}┌"
    printf "%*s" $((width-2)) | tr ' ' '─'
    printf "┐${NC}\n"
    
    # Conteúdo
    while IFS= read -r line; do
        printf "${color}│${NC} %-*s ${color}│${NC}\n" $((width-4)) "$line"
    done <<< "$text"
    
    # Linha inferior
    printf "${color}└"
    printf "%*s" $((width-2)) | tr ' ' '─'
    printf "┘${NC}\n"
}

# Função para confirmação com cores
confirm() {
    local message="$1"
    local default="${2:-n}"
    local response
    
    if [[ "$default" == "y" ]]; then
        printf "${YELLOW}%s [Y/n]: ${NC}" "$message"
    else
        printf "${YELLOW}%s [y/N]: ${NC}" "$message"
    fi
    
    read -r response
    
    if [[ -z "$response" ]]; then
        response="$default"
    fi
    
    [[ "$response" =~ ^[Yy]$ ]]
}

# Função para input com validação
input_with_validation() {
    local prompt="$1"
    local validation_regex="$2"
    local error_message="$3"
    local value
    
    while true; do
        printf "${CYAN}%s: ${NC}" "$prompt"
        read -r value
        
        if [[ "$value" =~ $validation_regex ]]; then
            echo "$value"
            return 0
        else
            print_error "$error_message"
        fi
    done
}

# Função para menu com seleção
select_option() {
    local prompt="$1"
    shift
    local options=("$@")
    local choice
    
    echo -e "${YELLOW}$prompt${NC}"
    for i in "${!options[@]}"; do
        printf "%d. %s\n" $((i+1)) "${options[i]}"
    done
    echo
    
    while true; do
        printf "${CYAN}Escolha uma opção [1-%d]: ${NC}" "${#options[@]}"
        read -r choice
        
        if [[ "$choice" =~ ^[0-9]+$ ]] && [[ "$choice" -ge 1 ]] && [[ "$choice" -le "${#options[@]}" ]]; then
            echo $((choice-1))
            return 0
        else
            print_error "Opção inválida! Digite um número entre 1 e ${#options[@]}"
        fi
    done
}

# Função para mostrar status
show_status() {
    local status="$1"
    local message="$2"
    
    case "$status" in
        "success"|"ok"|"done")
            printf "${GREEN}✅ %s${NC}\n" "$message"
            ;;
        "error"|"fail"|"failed")
            printf "${RED}❌ %s${NC}\n" "$message"
            ;;
        "warning"|"warn")
            printf "${YELLOW}⚠️  %s${NC}\n" "$message"
            ;;
        "info"|"information")
            printf "${BLUE}ℹ️  %s${NC}\n" "$message"
            ;;
        "working"|"progress")
            printf "${CYAN}🔄 %s${NC}\n" "$message"
            ;;
        *)
            printf "%s\n" "$message"
            ;;
    esac
}

# Função para limpar linha atual
clear_line() {
    printf "\r\033[K"
}

# Função para mover cursor
move_cursor() {
    local direction="$1"
    local count="${2:-1}"
    
    case "$direction" in
        "up")
            printf "\033[%dA" "$count"
            ;;
        "down")
            printf "\033[%dB" "$count"
            ;;
        "left")
            printf "\033[%dD" "$count"
            ;;
        "right")
            printf "\033[%dC" "$count"
            ;;
    esac
}
