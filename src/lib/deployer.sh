#!/bin/bash

# ============================================================================
# Container Deployer - Deploy para servidor de homologação
# ============================================================================

# Carregar dependências
source "$(dirname "${BASH_SOURCE[0]}")/colors.sh"
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# ============================================================================
# Configurações do deployer
# ============================================================================

STAGING_SERVER="*************"
STAGING_USER="root"
STAGING_BASE_PATH="/var/docker"
PNET_CONFIG_PATH="/etc/sysconfig/pnet"

# ============================================================================
# Funções de deploy
# ============================================================================

# Função principal de deploy
deploy_container() {
    local container_name="$1"
    local directories_file="$2"
    local work_dir="$3"
    
    print_header "DEPLOY PARA HOMOLOGAÇÃO"
    
    # Verificar conectividade SSH
    if ! check_ssh_connectivity "$STAGING_SERVER" "$STAGING_USER"; then
        return 1
    fi
    
    # Preparar estrutura no servidor remoto
    if ! prepare_remote_structure "$container_name"; then
        return 1
    fi
    
    # Sincronizar dados
    if ! sync_data "$container_name" "$directories_file" "$work_dir"; then
        return 1
    fi
    
    # Criar configuração PNET
    if ! create_pnet_config "$container_name" "$work_dir"; then
        return 1
    fi
    
    # Criar arquivos de rede
    if ! create_network_configs "$container_name"; then
        return 1
    fi
    
    # Deploy do container
    if ! deploy_pnet_container "$container_name"; then
        return 1
    fi
    
    print_success "Deploy concluído com sucesso!"
    show_deploy_summary "$container_name"
    
    return 0
}

# Preparar estrutura no servidor remoto
prepare_remote_structure() {
    local container_name="$1"
    
    print_step "Preparando estrutura no servidor remoto..."
    
    # Criar diretórios necessários
    local remote_commands=(
        "mkdir -p $STAGING_BASE_PATH/$container_name/{conf,data}"
        "mkdir -p $PNET_CONFIG_PATH"
        "chown -R root:root $STAGING_BASE_PATH/$container_name"
    )
    
    for cmd in "${remote_commands[@]}"; do
        if ! ssh "$STAGING_USER@$STAGING_SERVER" "$cmd"; then
            print_error "Falha ao executar: $cmd"
            return 1
        fi
    done
    
    print_success "Estrutura remota preparada"
    return 0
}

# Sincronizar dados
sync_data() {
    local container_name="$1"
    local directories_file="$2"
    local work_dir="$3"
    
    print_step "Sincronizando dados..."
    
    # Verificar se backup existe
    if [[ ! -d "$work_dir/backup" ]]; then
        print_error "Diretório de backup não encontrado: $work_dir/backup"
        return 1
    fi
    
    local remote_path="$STAGING_USER@$STAGING_SERVER:$STAGING_BASE_PATH/$container_name"
    
    # Sincronizar dados dos diretórios
    if [[ -d "$work_dir/backup/data" ]]; then
        print_step "Sincronizando diretórios de dados..."
        
        if rsync -avz --progress --delete \
            "$work_dir/backup/data/" \
            "$remote_path/data/"; then
            print_success "Dados sincronizados"
        else
            print_error "Falha na sincronização de dados"
            return 1
        fi
    fi
    
    # Sincronizar configurações
    if [[ -d "$work_dir/backup/conf" ]]; then
        print_step "Sincronizando arquivos de configuração..."
        
        if rsync -avz --progress --delete \
            "$work_dir/backup/conf/" \
            "$remote_path/conf/"; then
            print_success "Configurações sincronizadas"
        else
            print_error "Falha na sincronização de configurações"
            return 1
        fi
    fi
    
    return 0
}

# Criar configuração PNET
create_pnet_config() {
    local container_name="$1"
    local work_dir="$2"
    
    print_step "Criando configuração PNET..."
    
    # Gerar IP livre
    local container_ip
    container_ip=$(find_free_ip "172.16.14" 100 200)
    
    if [[ -z "$container_ip" ]]; then
        print_error "Não foi possível encontrar IP livre"
        return 1
    fi
    
    print_info "IP selecionado: $container_ip"
    
    # Criar arquivo de configuração local
    local pnet_config="$work_dir/pnet_config"
    
    cat > "$pnet_config" << EOF
#!/bin/bash
# /etc/sysconfig/pnet/$container_name

SO=centos7
TAG=latest
CONTAINER_NAME=$container_name
IMAGE_NAME=docker-registry.telemidia.net.br/$container_name

HOSTNAME=$container_name.pocos-net.com.br

BASE_PATH=/var/docker/$container_name
BIND_MOUNTS=()
BIND_MOUNTS+=( \$BASE_PATH/conf/ifcfg-eth0:/etc/sysconfig/network-scripts/ifcfg-eth0 )
BIND_MOUNTS+=( \$BASE_PATH/conf/ifcfg-eth1:/etc/sysconfig/network-scripts/ifcfg-eth1 )
BIND_MOUNTS+=( \$BASE_PATH/data/log:/var/log )
BIND_MOUNTS+=( \$BASE_PATH/data/tmp:/tmp )
EOF
    
    # Adicionar bind mounts para diretórios selecionados
    if [[ -f "$work_dir/selected_directories.txt" ]]; then
        while read -r path; do
            if [[ -n "$path" ]] && [[ "$path" != "/var/log" ]] && [[ "$path" != "/tmp" ]]; then
                echo "BIND_MOUNTS+=( \$BASE_PATH/data$path:$path )" >> "$pnet_config"
            fi
        done < <(jq -r '.selected_directories[].path' "$work_dir/selected_directories.txt" 2>/dev/null || true)
    fi
    
    cat >> "$pnet_config" << EOF

COMMAND=/usr/sbin/init

MEMORY=4g

NETWORKS=( docker_ger docker_pub)
HOSTS=( alternatemaster:************* myhosts.pocos-net.com.br:************* )
EOF
    
    # Copiar para servidor remoto
    if scp "$pnet_config" "$STAGING_USER@$STAGING_SERVER:$PNET_CONFIG_PATH/$container_name"; then
        print_success "Configuração PNET criada"
        
        # Salvar IP para uso posterior
        echo "$container_ip" > "$work_dir/container_ip"
    else
        print_error "Falha ao copiar configuração PNET"
        return 1
    fi
    
    return 0
}

# Criar configurações de rede
create_network_configs() {
    local container_name="$1"
    
    print_step "Criando configurações de rede..."
    
    # Ler IP do container
    local container_ip
    if [[ -f "$work_dir/container_ip" ]]; then
        container_ip=$(cat "$work_dir/container_ip")
    else
        print_error "IP do container não encontrado"
        return 1
    fi
    
    # Gerar MAC address aleatório
    local mac_address
    mac_address=$(generate_random_mac)
    
    # Criar configuração eth0 (vazia)
    local eth0_config="/tmp/ifcfg-eth0.$$"
    cat > "$eth0_config" << EOF
DEVICE=eth0
ONBOOT=yes
EOF
    
    # Criar configuração eth1
    local eth1_config="/tmp/ifcfg-eth1.$$"
    cat > "$eth1_config" << EOF
DEVICE=eth1
IPADDR=$container_ip
PREFIX=23
MACADDR=$mac_address
ONBOOT=yes
EOF
    
    # Copiar configurações para servidor remoto
    local remote_conf_path="$STAGING_BASE_PATH/$container_name/conf"
    
    if scp "$eth0_config" "$STAGING_USER@$STAGING_SERVER:$remote_conf_path/ifcfg-eth0" && \
       scp "$eth1_config" "$STAGING_USER@$STAGING_SERVER:$remote_conf_path/ifcfg-eth1"; then
        print_success "Configurações de rede criadas"
        print_info "IP: $container_ip"
        print_info "MAC: $mac_address"
    else
        print_error "Falha ao copiar configurações de rede"
        return 1
    fi
    
    # Limpar arquivos temporários
    rm -f "$eth0_config" "$eth1_config"
    
    return 0
}

# Deploy do container PNET
deploy_pnet_container() {
    local container_name="$1"
    
    print_step "Fazendo deploy do container PNET..."
    
    # Comandos para executar no servidor remoto
    local deploy_commands=(
        "pnet-container create $container_name"
        "pnet-container start $container_name"
    )
    
    for cmd in "${deploy_commands[@]}"; do
        print_step "Executando: $cmd"
        
        if ssh "$STAGING_USER@$STAGING_SERVER" "$cmd"; then
            print_success "Comando executado: $cmd"
        else
            print_error "Falha ao executar: $cmd"
            
            # Tentar obter logs de erro
            print_info "Verificando logs..."
            ssh "$STAGING_USER@$STAGING_SERVER" "journalctl -u pnet-container -n 20 --no-pager" || true
            
            return 1
        fi
    done
    
    # Aguardar container inicializar
    print_step "Aguardando container inicializar..."
    sleep 10
    
    # Verificar status do container
    if check_container_status "$container_name"; then
        print_success "Container iniciado com sucesso"
    else
        print_warning "Container pode não ter iniciado corretamente"
    fi
    
    return 0
}

# Verificar status do container
check_container_status() {
    local container_name="$1"
    
    print_step "Verificando status do container..."
    
    # Verificar se container está rodando
    local container_status
    container_status=$(ssh "$STAGING_USER@$STAGING_SERVER" "pnet-container status $container_name" 2>/dev/null || echo "unknown")
    
    print_info "Status: $container_status"
    
    # Verificar conectividade de rede
    if [[ -f "$work_dir/container_ip" ]]; then
        local container_ip
        container_ip=$(cat "$work_dir/container_ip")
        
        print_step "Testando conectividade para $container_ip..."
        
        if ping -c 3 -W 5 "$container_ip" >/dev/null 2>&1; then
            print_success "Container acessível via rede"
            return 0
        else
            print_warning "Container não responde ao ping"
        fi
    fi
    
    # Verificar logs do container
    print_step "Verificando logs do container..."
    ssh "$STAGING_USER@$STAGING_SERVER" "docker logs $container_name --tail 10" 2>/dev/null || true
    
    return 0
}

# Mostrar resumo do deploy
show_deploy_summary() {
    local container_name="$1"
    
    print_subheader "RESUMO DO DEPLOY"
    
    print_info "Container: $container_name"
    print_info "Servidor: $STAGING_SERVER"
    
    if [[ -f "$work_dir/container_ip" ]]; then
        local container_ip
        container_ip=$(cat "$work_dir/container_ip")
        print_info "IP: $container_ip"
        print_info "URL: http://$container_ip"
        print_info "SSH: ssh root@$container_ip"
    fi
    
    print_info "Configuração PNET: $PNET_CONFIG_PATH/$container_name"
    print_info "Dados: $STAGING_BASE_PATH/$container_name"
    
    echo
    print_info "Comandos úteis:"
    echo "  pnet-container status $container_name"
    echo "  pnet-container stop $container_name"
    echo "  pnet-container start $container_name"
    echo "  pnet-container restart $container_name"
    echo "  docker logs $container_name"
    echo "  docker exec -it $container_name bash"
}

# Rollback do deploy
rollback_deploy() {
    local container_name="$1"
    
    print_header "ROLLBACK DO DEPLOY"
    
    if ! confirm "Tem certeza que deseja fazer rollback do deploy de $container_name?"; then
        print_info "Rollback cancelado"
        return 0
    fi
    
    print_step "Fazendo rollback..."
    
    # Parar container
    ssh "$STAGING_USER@$STAGING_SERVER" "pnet-container stop $container_name" 2>/dev/null || true
    
    # Remover container
    ssh "$STAGING_USER@$STAGING_SERVER" "pnet-container remove $container_name" 2>/dev/null || true
    
    # Remover configuração
    ssh "$STAGING_USER@$STAGING_SERVER" "rm -f $PNET_CONFIG_PATH/$container_name" 2>/dev/null || true
    
    # Remover dados (com confirmação)
    if confirm "Deseja remover também os dados em $STAGING_BASE_PATH/$container_name?"; then
        ssh "$STAGING_USER@$STAGING_SERVER" "rm -rf $STAGING_BASE_PATH/$container_name" 2>/dev/null || true
        print_success "Dados removidos"
    else
        print_info "Dados preservados em $STAGING_BASE_PATH/$container_name"
    fi
    
    print_success "Rollback concluído"
}

# Atualizar container existente
update_container() {
    local container_name="$1"
    local work_dir="$2"
    
    print_header "ATUALIZAÇÃO DO CONTAINER"
    
    # Verificar se container existe
    local container_exists
    container_exists=$(ssh "$STAGING_USER@$STAGING_SERVER" "pnet-container status $container_name" 2>/dev/null || echo "not found")
    
    if [[ "$container_exists" == "not found" ]]; then
        print_error "Container $container_name não encontrado no servidor"
        return 1
    fi
    
    print_info "Container encontrado: $container_name"
    
    if ! confirm "Deseja atualizar o container existente?"; then
        print_info "Atualização cancelada"
        return 0
    fi
    
    # Parar container
    print_step "Parando container..."
    ssh "$STAGING_USER@$STAGING_SERVER" "pnet-container stop $container_name"
    
    # Fazer backup dos dados atuais
    print_step "Fazendo backup dos dados atuais..."
    local backup_timestamp
    backup_timestamp=$(date +%Y%m%d_%H%M%S)
    ssh "$STAGING_USER@$STAGING_SERVER" "cp -r $STAGING_BASE_PATH/$container_name $STAGING_BASE_PATH/${container_name}_backup_$backup_timestamp"
    
    # Sincronizar novos dados
    if sync_data "$container_name" "$work_dir/selected_directories.txt" "$work_dir"; then
        print_success "Dados atualizados"
    else
        print_error "Falha na atualização dos dados"
        
        # Restaurar backup
        print_step "Restaurando backup..."
        ssh "$STAGING_USER@$STAGING_SERVER" "rm -rf $STAGING_BASE_PATH/$container_name && mv $STAGING_BASE_PATH/${container_name}_backup_$backup_timestamp $STAGING_BASE_PATH/$container_name"
        
        return 1
    fi
    
    # Reiniciar container
    print_step "Reiniciando container..."
    ssh "$STAGING_USER@$STAGING_SERVER" "pnet-container start $container_name"
    
    # Verificar status
    if check_container_status "$container_name"; then
        print_success "Container atualizado com sucesso"
        
        # Remover backup se tudo deu certo
        if confirm "Deseja remover o backup criado?"; then
            ssh "$STAGING_USER@$STAGING_SERVER" "rm -rf $STAGING_BASE_PATH/${container_name}_backup_$backup_timestamp"
            print_success "Backup removido"
        else
            print_info "Backup preservado: ${container_name}_backup_$backup_timestamp"
        fi
    else
        print_error "Falha na atualização do container"
        return 1
    fi
    
    return 0
}

# Monitorar container
monitor_container() {
    local container_name="$1"
    local duration="${2:-300}"  # 5 minutos por padrão
    
    print_header "MONITORAMENTO DO CONTAINER"
    print_info "Monitorando $container_name por ${duration}s..."
    
    local start_time
    start_time=$(date +%s)
    local end_time=$((start_time + duration))
    
    while [[ $(date +%s) -lt $end_time ]]; do
        clear
        print_subheader "MONITORAMENTO - $(date)"
        
        # Status do container
        local status
        status=$(ssh "$STAGING_USER@$STAGING_SERVER" "pnet-container status $container_name" 2>/dev/null || echo "unknown")
        print_info "Status: $status"
        
        # Uso de recursos
        if ssh "$STAGING_USER@$STAGING_SERVER" "docker stats $container_name --no-stream --format 'table {{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}'" 2>/dev/null; then
            echo
        fi
        
        # Logs recentes
        print_subheader "LOGS RECENTES"
        ssh "$STAGING_USER@$STAGING_SERVER" "docker logs $container_name --tail 5" 2>/dev/null || true
        
        # Aguardar próxima atualização
        sleep 10
    done
    
    print_success "Monitoramento concluído"
}

# Gerar relatório de deploy
generate_deploy_report() {
    local container_name="$1"
    local work_dir="$2"
    
    local report_file="$work_dir/deploy_report.txt"
    
    cat > "$report_file" << EOF
# Deploy Report - $container_name

## Informações do Deploy
- Container: $container_name
- Servidor: $STAGING_SERVER
- Data: $(date)
- Usuário: $(whoami)

## Configurações
- IP: $(cat "$work_dir/container_ip" 2>/dev/null || echo "Não definido")
- Configuração PNET: $PNET_CONFIG_PATH/$container_name
- Dados: $STAGING_BASE_PATH/$container_name

## Status Atual
EOF
    
    # Adicionar status atual
    local status
    status=$(ssh "$STAGING_USER@$STAGING_SERVER" "pnet-container status $container_name" 2>/dev/null || echo "unknown")
    echo "- Status: $status" >> "$report_file"
    
    # Adicionar informações de recursos
    if ssh "$STAGING_USER@$STAGING_SERVER" "docker stats $container_name --no-stream --format '- CPU: {{.CPUPerc}}\n- Memória: {{.MemUsage}}\n- Rede: {{.NetIO}}\n- Disco: {{.BlockIO}}'" 2>/dev/null >> "$report_file"; then
        echo >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

## Comandos de Gerenciamento
\`\`\`bash
# Status
pnet-container status $container_name

# Parar/Iniciar
pnet-container stop $container_name
pnet-container start $container_name

# Logs
docker logs $container_name

# Acesso
docker exec -it $container_name bash
ssh root@$(cat "$work_dir/container_ip" 2>/dev/null || echo "IP_DO_CONTAINER")
\`\`\`

## Troubleshooting
- Verificar logs: docker logs $container_name
- Verificar configuração: cat $PNET_CONFIG_PATH/$container_name
- Verificar rede: ping $(cat "$work_dir/container_ip" 2>/dev/null || echo "IP_DO_CONTAINER")
- Verificar recursos: docker stats $container_name
EOF
    
    print_success "Relatório de deploy gerado: $report_file"
}
