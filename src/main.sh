#!/bin/bash

# ============================================================================
# Container Migration Tool - Main Script
# Automatiza a migração de servidores CentOS para containers Docker
# ============================================================================

set -euo pipefail

# Diretório base do projeto
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Carregar módulos
source "$SCRIPT_DIR/lib/colors.sh"
source "$SCRIPT_DIR/lib/utils.sh"
source "$SCRIPT_DIR/lib/scanner.sh"
source "$SCRIPT_DIR/lib/selector.sh"
source "$SCRIPT_DIR/lib/dockerfile_generator.sh"
source "$SCRIPT_DIR/lib/builder.sh"
source "$SCRIPT_DIR/lib/deployer.sh"

# Configurações globais
CONFIG_FILE="$PROJECT_ROOT/config/migration.conf"
WORK_DIR="$PROJECT_ROOT/work"
TEMPLATES_DIR="$PROJECT_ROOT/templates"

# Variáveis globais
CONTAINER_NAME=""
SCAN_RESULTS=""
SELECTED_PACKAGES=""
SELECTED_CONFIGS=""
SELECTED_DIRECTORIES=""

# ============================================================================
# Funções principais
# ============================================================================

show_banner() {
    echo -e "${BLUE}"
    echo "============================================================================"
    echo "                    CONTAINER MIGRATION TOOL v1.0"
    echo "         Automatiza a migração de servidores para containers Docker"
    echo "============================================================================"
    echo -e "${NC}"
}

show_main_menu() {
    clear
    show_banner
    
    echo -e "${YELLOW}MENU PRINCIPAL:${NC}"
    echo "1. Iniciar nova migração"
    echo "2. Continuar migração existente"
    echo "3. Configurações"
    echo "4. Ajuda"
    echo "5. Sair"
    echo
    echo -n "Escolha uma opção [1-5]: "
}

get_container_name() {
    echo -e "${CYAN}=== CONFIGURAÇÃO INICIAL ===${NC}"
    echo
    
    while true; do
        echo -n "Digite o nome do container: "
        read -r CONTAINER_NAME
        
        if [[ -z "$CONTAINER_NAME" ]]; then
            echo -e "${RED}Erro: Nome do container não pode estar vazio!${NC}"
            continue
        fi
        
        if [[ ! "$CONTAINER_NAME" =~ ^[a-zA-Z0-9][a-zA-Z0-9_-]*$ ]]; then
            echo -e "${RED}Erro: Nome inválido! Use apenas letras, números, _ e -${NC}"
            continue
        fi
        
        echo -e "${GREEN}Nome do container definido: ${CONTAINER_NAME}${NC}"
        break
    done
    
    # Criar diretório de trabalho
    WORK_DIR="$PROJECT_ROOT/work/$CONTAINER_NAME"
    mkdir -p "$WORK_DIR"
    
    # Salvar configuração
    echo "CONTAINER_NAME=\"$CONTAINER_NAME\"" > "$WORK_DIR/config.sh"
    echo "WORK_DIR=\"$WORK_DIR\"" >> "$WORK_DIR/config.sh"
    echo "CREATED_AT=\"$(date)\"" >> "$WORK_DIR/config.sh"
}

run_system_scan() {
    echo -e "${CYAN}=== VARREDURA DO SISTEMA ===${NC}"
    echo "Analisando o servidor CentOS..."
    echo
    
    # Executar varredura
    if ! scan_system "$WORK_DIR"; then
        echo -e "${RED}Erro durante a varredura do sistema!${NC}"
        return 1
    fi
    
    # Carregar resultados
    SCAN_RESULTS="$WORK_DIR/scan_results.json"
    
    echo -e "${GREEN}Varredura concluída com sucesso!${NC}"
    echo
    
    # Mostrar resumo
    show_scan_summary "$SCAN_RESULTS"
    
    echo
    echo -n "Pressione ENTER para continuar..."
    read -r
}

run_selection_process() {
    echo -e "${CYAN}=== SELEÇÃO DE COMPONENTES ===${NC}"
    echo "Escolha os componentes que deseja incluir no container:"
    echo
    
    # Seleção de pacotes
    if ! select_packages "$SCAN_RESULTS" "$WORK_DIR/selected_packages.txt"; then
        echo -e "${RED}Erro na seleção de pacotes!${NC}"
        return 1
    fi
    
    # Seleção de configurações
    if ! select_configs "$SCAN_RESULTS" "$WORK_DIR/selected_configs.txt"; then
        echo -e "${RED}Erro na seleção de configurações!${NC}"
        return 1
    fi
    
    # Seleção de diretórios
    if ! select_directories "$SCAN_RESULTS" "$WORK_DIR/selected_directories.txt"; then
        echo -e "${RED}Erro na seleção de diretórios!${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Seleção concluída!${NC}"
    echo
    echo -n "Pressione ENTER para continuar..."
    read -r
}

generate_dockerfile() {
    echo -e "${CYAN}=== GERAÇÃO DO DOCKERFILE ===${NC}"
    echo "Gerando Dockerfile baseado nas seleções..."
    echo
    
    if ! create_dockerfile \
        "$CONTAINER_NAME" \
        "$WORK_DIR/selected_packages.txt" \
        "$WORK_DIR/selected_configs.txt" \
        "$WORK_DIR/selected_directories.txt" \
        "$WORK_DIR/Dockerfile"; then
        echo -e "${RED}Erro na geração do Dockerfile!${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Dockerfile gerado: $WORK_DIR/Dockerfile${NC}"
    echo
    echo -n "Deseja visualizar o Dockerfile? [y/N]: "
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}--- Dockerfile ---${NC}"
        cat "$WORK_DIR/Dockerfile"
        echo
        echo -n "Pressione ENTER para continuar..."
        read -r
    fi
}

build_container_image() {
    echo -e "${CYAN}=== BUILD DA IMAGEM ===${NC}"
    echo -n "Deseja fazer o build da imagem do container? [y/N]: "
    read -r response
    
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "Build cancelado pelo usuário."
        return 0
    fi
    
    echo "Iniciando build da imagem..."
    echo
    
    if ! build_image "$CONTAINER_NAME" "$WORK_DIR"; then
        echo -e "${RED}Erro durante o build da imagem!${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Build concluído com sucesso!${NC}"
    echo
    echo -n "Pressione ENTER para continuar..."
    read -r
}

deploy_to_staging() {
    echo -e "${CYAN}=== DEPLOY PARA HOMOLOGAÇÃO ===${NC}"
    echo -n "Deseja fazer deploy no servidor de homologação (*************)? [y/N]: "
    read -r response
    
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "Deploy cancelado pelo usuário."
        return 0
    fi
    
    echo "Iniciando deploy para homologação..."
    echo
    
    if ! deploy_container \
        "$CONTAINER_NAME" \
        "$WORK_DIR/selected_directories.txt" \
        "$WORK_DIR"; then
        echo -e "${RED}Erro durante o deploy!${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Deploy concluído com sucesso!${NC}"
    echo
    echo -n "Pressione ENTER para continuar..."
    read -r
}

run_migration_workflow() {
    # Fluxo completo de migração
    get_container_name || return 1
    run_system_scan || return 1
    run_selection_process || return 1
    generate_dockerfile || return 1
    build_container_image || return 1
    deploy_to_staging || return 1
    
    echo -e "${GREEN}=== MIGRAÇÃO CONCLUÍDA ===${NC}"
    echo "Todos os passos foram executados com sucesso!"
    echo "Arquivos gerados em: $WORK_DIR"
    echo
}

load_existing_migration() {
    echo -e "${CYAN}=== CARREGAR MIGRAÇÃO EXISTENTE ===${NC}"
    echo
    
    # Listar migrações existentes
    if [[ ! -d "$PROJECT_ROOT/work" ]] || [[ -z "$(ls -A "$PROJECT_ROOT/work" 2>/dev/null)" ]]; then
        echo -e "${YELLOW}Nenhuma migração existente encontrada.${NC}"
        echo -n "Pressione ENTER para voltar..."
        read -r
        return 0
    fi
    
    echo "Migrações disponíveis:"
    local i=1
    local migrations=()
    
    for dir in "$PROJECT_ROOT/work"/*; do
        if [[ -d "$dir" && -f "$dir/config.sh" ]]; then
            local name=$(basename "$dir")
            migrations+=("$name")
            echo "$i. $name"
            ((i++))
        fi
    done
    
    if [[ ${#migrations[@]} -eq 0 ]]; then
        echo -e "${YELLOW}Nenhuma migração válida encontrada.${NC}"
        echo -n "Pressione ENTER para voltar..."
        read -r
        return 0
    fi
    
    echo
    echo -n "Escolha uma migração [1-${#migrations[@]}]: "
    read -r choice
    
    if [[ ! "$choice" =~ ^[0-9]+$ ]] || [[ "$choice" -lt 1 ]] || [[ "$choice" -gt ${#migrations[@]} ]]; then
        echo -e "${RED}Opção inválida!${NC}"
        return 1
    fi
    
    CONTAINER_NAME="${migrations[$((choice-1))]}"
    WORK_DIR="$PROJECT_ROOT/work/$CONTAINER_NAME"
    
    # Carregar configuração
    source "$WORK_DIR/config.sh"
    
    echo -e "${GREEN}Migração carregada: $CONTAINER_NAME${NC}"
    echo
    
    # Menu de continuação
    while true; do
        echo "O que deseja fazer?"
        echo "1. Continuar do ponto onde parou"
        echo "2. Refazer varredura"
        echo "3. Refazer seleções"
        echo "4. Gerar novo Dockerfile"
        echo "5. Fazer build"
        echo "6. Fazer deploy"
        echo "7. Voltar"
        echo
        echo -n "Escolha uma opção [1-7]: "
        read -r option
        
        case $option in
            1)
                # Detectar último passo executado e continuar
                continue_migration
                break
                ;;
            2)
                run_system_scan && run_selection_process && generate_dockerfile
                break
                ;;
            3)
                run_selection_process && generate_dockerfile
                break
                ;;
            4)
                generate_dockerfile
                break
                ;;
            5)
                build_container_image
                break
                ;;
            6)
                deploy_to_staging
                break
                ;;
            7)
                break
                ;;
            *)
                echo -e "${RED}Opção inválida!${NC}"
                ;;
        esac
    done
}

continue_migration() {
    # Detectar último passo executado
    if [[ ! -f "$WORK_DIR/scan_results.json" ]]; then
        echo "Continuando da varredura..."
        run_system_scan || return 1
    fi
    
    if [[ ! -f "$WORK_DIR/selected_packages.txt" ]]; then
        echo "Continuando da seleção..."
        run_selection_process || return 1
    fi
    
    if [[ ! -f "$WORK_DIR/Dockerfile" ]]; then
        echo "Continuando da geração do Dockerfile..."
        generate_dockerfile || return 1
    fi
    
    echo "Continuando do build/deploy..."
    build_container_image || return 1
    deploy_to_staging || return 1
}

show_help() {
    clear
    show_banner
    
    echo -e "${YELLOW}AJUDA - Container Migration Tool${NC}"
    echo
    echo "Este tool automatiza a migração de servidores CentOS para containers Docker."
    echo
    echo -e "${CYAN}Fluxo de trabalho:${NC}"
    echo "1. Varredura do sistema - Analisa pacotes, configurações e diretórios"
    echo "2. Seleção de componentes - Escolha o que incluir no container"
    echo "3. Geração do Dockerfile - Cria o Dockerfile baseado nas seleções"
    echo "4. Build da imagem - Constrói a imagem Docker"
    echo "5. Deploy - Envia para o servidor de homologação"
    echo
    echo -e "${CYAN}Arquivos gerados:${NC}"
    echo "- Dockerfile: Definição da imagem"
    echo "- Scripts de configuração"
    echo "- Arquivos de backup dos dados"
    echo
    echo -e "${CYAN}Servidor de destino:${NC}"
    echo "- IP: *************"
    echo "- Diretório: /var/docker/\$CONTAINER_NAME"
    echo
    echo -n "Pressione ENTER para voltar..."
    read -r
}

# ============================================================================
# Loop principal
# ============================================================================

main() {
    # Verificar dependências
    check_dependencies || exit 1
    
    # Criar diretórios necessários
    mkdir -p "$PROJECT_ROOT"/{work,config,templates,logs}
    
    while true; do
        show_main_menu
        read -r choice
        
        case $choice in
            1)
                run_migration_workflow
                ;;
            2)
                load_existing_migration
                ;;
            3)
                echo "Configurações ainda não implementadas"
                echo -n "Pressione ENTER para continuar..."
                read -r
                ;;
            4)
                show_help
                ;;
            5)
                echo -e "${GREEN}Saindo...${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}Opção inválida!${NC}"
                sleep 1
                ;;
        esac
    done
}

# Executar apenas se chamado diretamente
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
