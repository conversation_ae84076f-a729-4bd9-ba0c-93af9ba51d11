# ============================================================================
# Container Migration Tool - Configuration File
# ============================================================================

# Servidor de homologação
STAGING_SERVER="************"
STAGING_USER="root"
STAGING_BASE_PATH="/var/docker"

# Registry Docker
DOCKER_REGISTRY="docker-registry.telemidia.net.br"

# Configurações de rede
NETWORK_BASE="172.16.14"
IP_RANGE_START=100
IP_RANGE_END=200

# Configurações de build
BUILD_TIMEOUT=3600
MAX_CONTEXT_SIZE_MB=1024

# Configurações de backup
BACKUP_EXCLUDE_PATTERNS=(
    "*.log"
    "tmp/*"
    "cache/*"
    "*.tmp"
    "*.temp"
)

# Diretórios a serem sempre excluídos
EXCLUDE_DIRS=(
    "/proc"
    "/sys"
    "/dev"
    "/run"
    "/tmp"
    "/var/tmp"
    "/var/cache"
    "/var/log"
)

# Pacotes base que não devem ser incluídos
BASE_PACKAGES_EXCLUDE=(
    "kernel*"
    "grub*"
    "dracut*"
    "plymouth*"
    "NetworkManager*"
)

# Configurações de log
LOG_LEVEL="INFO"
LOG_FILE="/var/log/container-migration.log"
LOG_MAX_SIZE_MB=10
LOG_MAX_FILES=5

# Configurações de segurança
REQUIRE_ROOT=true
VERIFY_SSH_KEYS=true
BACKUP_BEFORE_DEPLOY=true

# Timeouts (em segundos)
SSH_TIMEOUT=30
RSYNC_TIMEOUT=1800
DOCKER_BUILD_TIMEOUT=3600
CONTAINER_START_TIMEOUT=120

# Recursos mínimos necessários
MIN_FREE_MEMORY_MB=1024
MIN_FREE_DISK_MB=2048
MAX_LOAD_AVERAGE=2.0
