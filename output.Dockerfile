# Generated by Enhanced Server Analysis Script
# Date: seg set 15 22:36:38 -03 2025
# Base System: centos 8
# Custom Packages: 74

FROM rockylinux:8

LABEL maintainer="server-replica@local"
LABEL description="Replica of production server portal-vendas.pocos-net.com.br"
LABEL created="2025-09-15"

ENV container docker
ENV DEBIAN_FRONTEND=noninteractive

# Instalar repositórios adicionais
RUN yum -y update && yum -y install epel-release yum-utils

# Instalar pacotes personalizados detectados
RUN yum -y install bind-export-libs certbot emacs-filesystem epel-release git git-core git-core-doc httpd httpd-filesystem httpd-tools && yum clean all
RUN yum -y install iptables-libs nano nginx-filesystem php php-cli php-common php-fpm php-gd php-json php-mbstring && yum clean all
RUN yum -y install php-mysqlnd php-pdo php-pgsql php-xml python36 python3-acme python3-audit python3-augeas python3-certbot python3-certbot-apache && yum clean all
RUN yum -y install python3-cffi python3-chardet python3-configargparse python3-configobj python3-cryptography python3-distro python3-dnf python3-gpg python3-hawkey python3-idna && yum clean all
RUN yum -y install python3-josepy python3-libcomps python3-libdnf python3-libs python3-libselinux python3-libsemanage python3-parsedatetime python3-pip python3-pip-wheel python3-ply && yum clean all
RUN yum -y install python3-policycoreutils python3-pycparser python3-pyOpenSSL python3-pyrfc3339 python3-pysocks python3-pytz python3-requests python3-requests-toolbelt python3-rpm python3-setools && yum clean all
RUN yum -y install python3-setuptools python3-setuptools-wheel python3-six python3-urllib3 python3-zope-component python3-zope-event python3-zope-interface python-josepy-doc rsync vim-common && yum clean all
RUN yum -y install vim-enhanced vim-filesystem zabbix-agent zabbix-release && yum clean all

# Configurar crontabs
RUN yum -y install cronie || apt-get update && apt-get install -y cron
COPY crontabs.txt /tmp/crontabs.txt
RUN while IFS= read -r line; do
  if [[ $line == "# ROOT CRONTAB" ]]; then
    mode="root"
  elif [[ $line == "# CRONTAB DO USUÁRIO:"* ]]; then
    mode=$(echo "$line" | cut -d: -f2 | tr -d ' ')
  elif [[ $line != "#"* ]] && [[ -n "$line" ]]; then
    echo "$line" | crontab -u ${mode:-root} - 2>/dev/null || true
  fi
done < /tmp/crontabs.txt

# Copiar diretórios de aplicação e configuração
COPY ./backup/opt /opt/
COPY ./backup/var/www /var/www/
COPY ./backup/etc /etc/
COPY ./backup/usr/local /usr/local/

# Habilitar serviços detectados
RUN systemctl enable bacula-fd.service 2>/dev/null || true
RUN systemctl enable crond.service 2>/dev/null || true
RUN systemctl enable httpd.service 2>/dev/null || true
RUN systemctl enable import-state.service 2>/dev/null || true
RUN systemctl enable kdump.service 2>/dev/null || true
RUN systemctl enable loadmodules.service 2>/dev/null || true
RUN systemctl enable nis-domainname.service 2>/dev/null || true
RUN systemctl enable nslcd.service 2>/dev/null || true
RUN systemctl enable selinux-autorelabel-mark.service 2>/dev/null || true
RUN systemctl enable sshd.service 2>/dev/null || true
RUN systemctl enable zabbix-agent.service 2>/dev/null || true

# Expor portas detectadas
EXPOSE 22
EXPOSE 80
EXPOSE 443
EXPOSE 9102
EXPOSE 10050
EXPOSE 33750
EXPOSE 34763
EXPOSE 35235
EXPOSE 35845
EXPOSE 37458
EXPOSE 38767
EXPOSE 41454
EXPOSE 42001
EXPOSE 42043
EXPOSE 43999
EXPOSE 44604

# Volumes para dados persistentes
VOLUME ["/var/log", "/var/lib/mysql", "/var/www", "/etc"]

# Healthcheck básico
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Script de inicialização
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["/sbin/init"]
