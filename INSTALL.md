# Guia de Instalação - Container Migration Tool

## 🚀 Instalação Rápida

### 1. Pré-requisitos

Certifique-se de que o sistema atende aos requisitos:

```bash
# Verificar sistema operacional
cat /etc/os-release

# Verificar se é root
whoami

# Verificar conectividade com servidor de homologação
ping -c 3 *************
```

### 2. Instalar Dependências

```bash
# CentOS/RHEL/AlmaLinux/Rocky
yum update -y
yum install -y docker rsync openssh-clients jq curl git bc

# Iniciar Docker
systemctl enable docker
systemctl start docker

# Verificar Docker
docker --version
docker info
```

### 3. Configurar SSH

```bash
# Gerar chave SSH se não existir
if [[ ! -f ~/.ssh/id_rsa ]]; then
    ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
fi

# Copiar chave para servidor de homologação
ssh-copy-id root@*************

# Testar conectividade
ssh root@************* "echo 'SSH OK'"
```

### 4. Baixar e Instalar a Ferramenta

```bash
# Criar diretório
mkdir -p /opt/container-migration-tool
cd /opt/container-migration-tool

# Se usando git (recomendado)
git clone <repositorio> .

# Ou copiar arquivos manualmente
# scp -r user@source:/path/to/tool/* .

# Tornar executável
chmod +x migrate.sh
chmod +x src/main.sh
```

### 5. Verificar Instalação

```bash
# Testar execução
./migrate.sh

# Deve aparecer o menu principal
```

## 🔧 Configuração Avançada

### Personalizar Configurações

```bash
# Editar arquivo de configuração
nano config/migration.conf

# Principais configurações:
# - STAGING_SERVER: IP do servidor de homologação
# - DOCKER_REGISTRY: Registry Docker
# - NETWORK_BASE: Base da rede para IPs
# - IP_RANGE_START/END: Faixa de IPs disponíveis
```

### Configurar Logs

```bash
# Criar diretório de logs
mkdir -p /var/log

# Configurar rotação de logs (opcional)
cat > /etc/logrotate.d/container-migration << 'EOF'
/var/log/container-migration.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
```

### Configurar Variáveis de Ambiente (Opcional)

```bash
# Adicionar ao ~/.bashrc ou /etc/environment
export MIGRATION_DEBUG=0
export MIGRATION_STAGING_SERVER="*************"
export MIGRATION_DOCKER_REGISTRY="docker-registry.telemidia.net.br"
```

## 🧪 Teste da Instalação

### Teste Básico

```bash
# Executar em modo de teste
cd /opt/container-migration-tool
./migrate.sh

# Seguir o menu:
# 1. Iniciar nova migração
# 2. Digite um nome de teste: "test-container"
# 3. Aguardar varredura
# 4. Cancelar antes do build (Ctrl+C)
```

### Teste de Conectividade

```bash
# Testar SSH
ssh root@************* "pnet-container list"

# Testar Docker Registry (se configurado)
docker pull docker-registry.telemidia.net.br/alma-base:latest
```

### Teste de Recursos

```bash
# Verificar espaço em disco (mínimo 2GB)
df -h

# Verificar memória (mínimo 1GB livre)
free -h

# Verificar carga do sistema
uptime
```

## 🔍 Verificação de Problemas

### Problemas Comuns e Soluções

#### 1. Docker não funciona
```bash
# Verificar status
systemctl status docker

# Reiniciar se necessário
systemctl restart docker

# Verificar permissões
usermod -aG docker root
```

#### 2. SSH não conecta
```bash
# Verificar conectividade
telnet ************* 22

# Verificar chaves
ssh -v root@*************

# Reconfigurar se necessário
ssh-keygen -R *************
ssh-copy-id root@*************
```

#### 3. Dependências faltando
```bash
# Verificar cada dependência
for cmd in docker rsync ssh jq curl git bc; do
    if command -v $cmd >/dev/null; then
        echo "✅ $cmd: OK"
    else
        echo "❌ $cmd: FALTANDO"
    fi
done

# Instalar faltantes
yum install -y <pacote_faltante>
```

#### 4. Permissões incorretas
```bash
# Verificar se é root
if [[ $EUID -eq 0 ]]; then
    echo "✅ Executando como root"
else
    echo "❌ Deve executar como root"
    echo "Use: sudo ./migrate.sh"
fi

# Corrigir permissões dos arquivos
chmod +x migrate.sh src/main.sh
chmod -R 755 src/lib/
```

## 📋 Checklist de Instalação

- [ ] Sistema operacional compatível (CentOS/RHEL/AlmaLinux/Rocky)
- [ ] Acesso root disponível
- [ ] Docker instalado e funcionando
- [ ] Dependências instaladas (rsync, ssh, jq, curl, git, bc)
- [ ] SSH configurado para *************
- [ ] Ferramenta baixada e com permissões corretas
- [ ] Configurações personalizadas (se necessário)
- [ ] Teste básico executado com sucesso

## 🆘 Suporte

### Logs para Análise
```bash
# Coletar informações do sistema
cat > /tmp/system-info.txt << EOF
# Sistema
$(cat /etc/os-release)

# Recursos
$(free -h)
$(df -h)
$(uptime)

# Docker
$(docker --version)
$(docker info 2>&1 | head -20)

# Conectividade
$(ping -c 3 ************* 2>&1)
$(ssh -o ConnectTimeout=5 root@************* "echo SSH OK" 2>&1)

# Dependências
$(for cmd in docker rsync ssh jq curl git bc; do echo "$cmd: $(command -v $cmd || echo 'NOT FOUND')"; done)
EOF

echo "Informações coletadas em: /tmp/system-info.txt"
```

### Contato
- **Equipe**: Infraestrutura PNET
- **Logs**: Sempre incluir logs relevantes
- **Contexto**: Descrever o que estava tentando fazer

---

**Próximo passo**: Após instalação bem-sucedida, consulte o [README.md](README.md) para instruções de uso.
