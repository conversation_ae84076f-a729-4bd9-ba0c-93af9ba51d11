#!/bin/bash

# Script para fazer backup dos diretórios importantes
# Execute este script no servidor original antes de usar o Dockerfile

BACKUP_DIR="./backup"
mkdir -p "$BACKUP_DIR"

DIRS_TO_BACKUP=(/opt /srv /var/www /etc /usr/local /home/<USER>/app)

for dir in "${DIRS_TO_BACKUP[@]}"; do
    for realdir in $dir; do
        if [ -d "$realdir" ]; then
            echo "Fazendo backup de $realdir..."
            mkdir -p "$BACKUP_DIR$realdir"
            rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \
                  "$realdir/" "$BACKUP_DIR$realdir/" 2>/dev/null || true
        fi
    done
done

echo "Backup completo em: $BACKUP_DIR"
