#!/bin/bash

# ============================================================================
# Teste Rápido da Ferramenta
# ============================================================================

set -e

echo "🧪 Testando Container Migration Tool..."
echo

# Verificar estrutura de arquivos
echo "📁 Verificando estrutura de arquivos..."
required_files=(
    "migrate.sh"
    "src/main.sh"
    "src/lib/colors.sh"
    "src/lib/utils.sh"
    "src/lib/scanner.sh"
    "src/lib/selector.sh"
    "src/lib/dockerfile_generator.sh"
    "src/lib/builder.sh"
    "src/lib/deployer.sh"
    "config/migration.conf"
    "templates/ifcfg-eth0.template"
    "templates/ifcfg-eth1.template"
    "templates/pnet_config.template"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file"
        missing_files+=("$file")
    fi
done

if [[ ${#missing_files[@]} -gt 0 ]]; then
    echo
    echo "❌ Arquivos faltando: ${missing_files[*]}"
    exit 1
fi

echo
echo "📋 Verificando dependências..."

# Verificar dependências
dependencies=(
    "docker:Docker"
    "rsync:Rsync"
    "ssh:SSH Client"
    "jq:JSON Processor"
    "curl:cURL"
    "git:Git"
)

missing_deps=()
for dep in "${dependencies[@]}"; do
    cmd="${dep%:*}"
    name="${dep#*:}"
    
    if command -v "$cmd" >/dev/null 2>&1; then
        echo "  ✅ $name ($cmd)"
    else
        echo "  ❌ $name ($cmd)"
        missing_deps+=("$cmd")
    fi
done

if [[ ${#missing_deps[@]} -gt 0 ]]; then
    echo
    echo "⚠️  Dependências faltando: ${missing_deps[*]}"
    echo "   Instale com: yum install -y ${missing_deps[*]}"
fi

echo
echo "🔧 Verificando permissões..."

# Verificar permissões
if [[ -x "migrate.sh" ]]; then
    echo "  ✅ migrate.sh é executável"
else
    echo "  ❌ migrate.sh não é executável"
    echo "     Execute: chmod +x migrate.sh"
fi

if [[ -x "src/main.sh" ]]; then
    echo "  ✅ src/main.sh é executável"
else
    echo "  ❌ src/main.sh não é executável"
    echo "     Execute: chmod +x src/main.sh"
fi

echo
echo "🌐 Verificando conectividade..."

# Testar conectividade básica
if ping -c 1 -W 3 172.16.14.81 >/dev/null 2>&1; then
    echo "  ✅ Servidor 172.16.14.81 acessível"
else
    echo "  ⚠️  Servidor 172.16.14.81 não acessível"
    echo "     Verifique conectividade de rede"
fi

# Testar SSH (se possível)
if ssh -o ConnectTimeout=5 -o BatchMode=yes root@172.16.14.81 exit 2>/dev/null; then
    echo "  ✅ SSH para root@172.16.14.81 OK"
else
    echo "  ⚠️  SSH para root@172.16.14.81 não configurado"
    echo "     Configure com: ssh-copy-id root@172.16.14.81"
fi

echo
echo "💾 Verificando recursos do sistema..."

# Verificar recursos
mem_available=$(free -m | awk '/^Mem:/ {print $7}')
if [[ "$mem_available" -gt 1024 ]]; then
    echo "  ✅ Memória disponível: ${mem_available}MB"
else
    echo "  ⚠️  Pouca memória disponível: ${mem_available}MB (recomendado: >1GB)"
fi

disk_available=$(df -m . | awk 'NR==2 {print $4}')
if [[ "$disk_available" -gt 2048 ]]; then
    echo "  ✅ Espaço em disco: ${disk_available}MB"
else
    echo "  ⚠️  Pouco espaço em disco: ${disk_available}MB (recomendado: >2GB)"
fi

echo
echo "🧪 Teste de sintaxe dos scripts..."

# Verificar sintaxe dos scripts bash
for script in migrate.sh src/main.sh src/lib/*.sh; do
    if bash -n "$script" 2>/dev/null; then
        echo "  ✅ $script"
    else
        echo "  ❌ $script (erro de sintaxe)"
    fi
done

echo
echo "📊 Resumo do teste:"

# Contar sucessos e falhas
total_checks=0
passed_checks=0

# Arquivos
total_checks=$((total_checks + ${#required_files[@]}))
passed_checks=$((passed_checks + ${#required_files[@]} - ${#missing_files[@]}))

# Dependências
total_checks=$((total_checks + ${#dependencies[@]}))
passed_checks=$((passed_checks + ${#dependencies[@]} - ${#missing_deps[@]}))

# Outros checks (permissões, conectividade, recursos, sintaxe)
# Simplificado para o exemplo
if [[ ${#missing_files[@]} -eq 0 && ${#missing_deps[@]} -eq 0 ]]; then
    echo "  ✅ Ferramenta pronta para uso!"
    echo "  📝 Execute: sudo ./migrate.sh"
else
    echo "  ⚠️  Corrija os problemas identificados antes de usar"
    echo "  📖 Consulte INSTALL.md para instruções detalhadas"
fi

echo
echo "🔗 Links úteis:"
echo "  📖 Documentação: README.md"
echo "  🛠️  Instalação: INSTALL.md"
echo "  ⚙️  Configuração: config/migration.conf"

echo
echo "✨ Teste concluído!"
