#!/bin/bash
# /etc/sysconfig/pnet/{{CONTAINER_NAME}}

SO=centos7
TAG=latest
CONTAINER_NAME={{CONTAINER_NAME}}
IMAGE_NAME=docker-registry.telemidia.net.br/{{CONTAINER_NAME}}

HOSTNAME={{CONTAINER_NAME}}.pocos-net.com.br

BASE_PATH=/var/docker/{{CONTAINER_NAME}}
BIND_MOUNTS=()
BIND_MOUNTS+=( $BASE_PATH/conf/ifcfg-eth0:/etc/sysconfig/network-scripts/ifcfg-eth0 )
BIND_MOUNTS+=( $BASE_PATH/conf/ifcfg-eth1:/etc/sysconfig/network-scripts/ifcfg-eth1 )
BIND_MOUNTS+=( $BASE_PATH/data/log:/var/log )
BIND_MOUNTS+=( $BASE_PATH/data/tmp:/tmp )
{{ADDITIONAL_MOUNTS}}

COMMAND=/usr/sbin/init

MEMORY=4g

NETWORKS=( docker_ger docker_pub)
HOSTS=( alternatemaster:************* myhosts.pocos-net.com.br:************* )
