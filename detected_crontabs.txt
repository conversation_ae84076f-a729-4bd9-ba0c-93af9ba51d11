# ROOT CRONTAB
# Renova certificados SSL dias 13 e 28 
0 2 13 * * certbot renew
0 2 28 * * certbot renew

#* * * * * php /var/www/html/contratar/cronjobs/mail_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/cronjobs/mail_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/cronjobs/mail_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/cronjobs/mail_checker.php
#
#* * * * * php /var/www/html/contratar/cronjobs/docs_reenvio_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/cronjobs/docs_reenvio_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/cronjobs/docs_reenvio_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/cronjobs/docs_reenvio_checker.php
#
#* * * * * php /var/www/html/contratar/cronjobs/conclusao_mail_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/cronjobs/conclusao_mail_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/cronjobs/conclusao_mail_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/cronjobs/conclusao_mail_checker.php
#
#
#
#
#* * * * * php /var/www/html/contratar/contratar/mails/mail_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/contratar/mails/mail_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/contratar/mails/mail_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/contratar/mails/mail_checker.php
#
#* * * * * php /var/www/html/contratar/contratar/mails/docs_reenvio_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/contratar/mails/docs_reenvio_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/contratar/mails/docs_reenvio_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/contratar/mails/docs_reenvio_checker.php
#
#* * * * * php /var/www/html/contratar/contratar/mails/conclusao_mail_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/contratar/mails/conclusao_mail_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/contratar/mails/conclusao_mail_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/contratar/mails/conclusao_mail_checker.php
#
* * * * * php /var/www/html/contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 15; php /var/www/html/contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 30; php /var/www/html/contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 45; php /var/www/html/contratar-telemidia/common/cronjobs/mailSender.php


* * * * * php /var/www/html/dev-contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 15; php /var/www/html/dev-contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 30; php /var/www/html/dev-contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 45; php /var/www/html/dev-contratar-telemidia/common/cronjobs/mailSender.php

# CRONTAB DO USUÁRIO: root
# Renova certificados SSL dias 13 e 28 
0 2 13 * * certbot renew
0 2 28 * * certbot renew

#* * * * * php /var/www/html/contratar/cronjobs/mail_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/cronjobs/mail_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/cronjobs/mail_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/cronjobs/mail_checker.php
#
#* * * * * php /var/www/html/contratar/cronjobs/docs_reenvio_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/cronjobs/docs_reenvio_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/cronjobs/docs_reenvio_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/cronjobs/docs_reenvio_checker.php
#
#* * * * * php /var/www/html/contratar/cronjobs/conclusao_mail_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/cronjobs/conclusao_mail_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/cronjobs/conclusao_mail_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/cronjobs/conclusao_mail_checker.php
#
#
#
#
#* * * * * php /var/www/html/contratar/contratar/mails/mail_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/contratar/mails/mail_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/contratar/mails/mail_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/contratar/mails/mail_checker.php
#
#* * * * * php /var/www/html/contratar/contratar/mails/docs_reenvio_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/contratar/mails/docs_reenvio_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/contratar/mails/docs_reenvio_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/contratar/mails/docs_reenvio_checker.php
#
#* * * * * php /var/www/html/contratar/contratar/mails/conclusao_mail_checker.php
#* * * * * sleep 15; php /var/www/html/contratar/contratar/mails/conclusao_mail_checker.php
#* * * * * sleep 30; php /var/www/html/contratar/contratar/mails/conclusao_mail_checker.php
#* * * * * sleep 45; php /var/www/html/contratar/contratar/mails/conclusao_mail_checker.php
#
* * * * * php /var/www/html/contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 15; php /var/www/html/contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 30; php /var/www/html/contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 45; php /var/www/html/contratar-telemidia/common/cronjobs/mailSender.php


* * * * * php /var/www/html/dev-contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 15; php /var/www/html/dev-contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 30; php /var/www/html/dev-contratar-telemidia/common/cronjobs/mailSender.php
* * * * * sleep 45; php /var/www/html/dev-contratar-telemidia/common/cronjobs/mailSender.php

# CRON.D FILES
## 0hourly
# Run the hourly jobs
SHELL=/bin/bash
PATH=/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=root
01 * * * * root run-parts /etc/cron.hourly

