#!/bin/bash

# ============================================================================
# Container Migration Tool - Script Principal
# Automatiza a migração de servidores CentOS para containers Docker
# ============================================================================

set -euo pipefail

# Diretório do script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Verificar se está sendo executado como root
if [[ $EUID -ne 0 ]]; then
    echo "❌ Este script deve ser executado como root"
    echo "   Use: sudo $0"
    exit 1
fi

# Verificar se o diretório src existe
if [[ ! -d "$SCRIPT_DIR/src" ]]; then
    echo "❌ Diretório src não encontrado"
    echo "   Certifique-se de que está executando o script do diretório correto"
    exit 1
fi

# Executar o script principal
exec "$SCRIPT_DIR/src/main.sh" "$@"
